const DeliveryPartner = require('../models/DeliveryPartner');
const Order = require('../models/Order');
const { processOrderInvoice } = require('../services/invoiceService');

/**
 * Get delivery partner's assigned orders and available unassigned orders
 * @route GET /api/delivery/orders
 * @access Private (Delivery Partner only)
 */
const getDeliveryPartnerOrders = async (req, res) => {
    try {
        console.log(`Fetching orders for delivery partner ID: ${req.user._id}`);

        // First check if the delivery partner exists
        const partner = await DeliveryPartner.findById(req.user._id);
        if (!partner) {
            console.error(`Delivery partner with ID ${req.user._id} not found`);
            return res.status(404).json({
                message: 'Delivery partner not found',
                partnerId: req.user._id
            });
        }

        console.log(`Found delivery partner: ${partner.name}, isAvailable: ${partner.isAvailable}`);

        // Only show orders to available delivery partners
        if (!partner.isAvailable) {
            console.log(`Delivery partner ${req.user._id} is not available, returning only assigned orders`);
            // Return only orders already assigned to this partner
            const assignedOrders = await Order.find({
                deliveryPartner: req.user._id,
                status: { $in: ['OUT_FOR_DELIVERY', 'DELIVERED'] }
            })
            .populate('userId', 'name number')
            .sort({ createdAt: -1 });

            return res.status(200).json(assignedOrders);
        }

        // Get orders already assigned to this delivery partner
        const assignedOrders = await Order.find({
            deliveryPartner: req.user._id
        })
        .populate('userId', 'name number')
        .sort({ createdAt: -1 });

        console.log(`Found ${assignedOrders.length} assigned orders for delivery partner ${req.user._id}`);

        // Get unassigned orders with status PLACED or CONFIRMED
        const unassignedOrders = await Order.find({
            deliveryPartner: { $exists: false },
            status: { $in: ['PLACED', 'CONFIRMED'] }
        })
        .populate('userId', 'name number')
        .sort({ createdAt: -1 });

        console.log(`Found ${unassignedOrders.length} unassigned orders that can be picked up`);

        // Add a flag to distinguish between assigned and unassigned orders
        const formattedAssignedOrders = assignedOrders.map(order => {
            const orderObj = order.toObject();
            orderObj.isAssigned = true;
            return orderObj;
        });

        const formattedUnassignedOrders = unassignedOrders.map(order => {
            const orderObj = order.toObject();
            orderObj.isAssigned = false;
            return orderObj;
        });

        // Combine both types of orders
        const allOrders = [...formattedAssignedOrders, ...formattedUnassignedOrders];

        // Log the total number of orders being returned
        console.log(`Returning ${allOrders.length} total orders (${formattedAssignedOrders.length} assigned, ${formattedUnassignedOrders.length} unassigned)`);

        // Return all orders
        res.status(200).json(allOrders);
    } catch (error) {
        console.error('Error fetching delivery partner orders:', error);
        res.status(500).json({
            message: 'Server error',
            error: error.message
        });
    }
};

/**
 * Update order status or assign order to delivery partner
 * @route PUT /api/delivery/orders/:id/status
 * @access Private (Delivery Partner only)
 */
const updateOrderStatus = async (req, res) => {
    try {
        const { status, assignToMe } = req.body;
        const orderId = req.params.id;

        console.log(`Updating order ${orderId} status to ${status}, assignToMe: ${assignToMe === true ? 'true' : 'false'}`);

        // Log the request body for debugging
        console.log('Request body:', JSON.stringify(req.body, null, 2));

        const order = await Order.findById(orderId).populate('userId', 'name number');
        if (!order) {
            return res.status(404).json({ message: 'Order not found' });
        }

        // Handle order assignment case
        if (assignToMe === true || req.body.assignToMe === true) {
            console.log('Handling order assignment request');
            console.log(`Delivery partner ${req.user._id} is requesting to be assigned to order ${orderId}`);

            // Check if order is already assigned to a delivery partner
            if (order.deliveryPartner) {
                console.log(`Order ${orderId} is already assigned to delivery partner ${order.deliveryPartner}`);
                return res.status(400).json({
                    message: 'Order is already assigned to a delivery partner',
                    currentAssignee: order.deliveryPartner
                });
            }

            // Check if order status is valid for assignment
            if (!['PLACED', 'CONFIRMED'].includes(order.status)) {
                console.log(`Order ${orderId} has invalid status for assignment: ${order.status}`);
                return res.status(400).json({
                    message: `Cannot assign order with status ${order.status}`,
                    currentStatus: order.status
                });
            }

            // Assign the order to this delivery partner
            order.deliveryPartner = req.user._id;
            order.deliveryPartnerAssignedAt = new Date();
            order.status = 'OUT_FOR_DELIVERY';
            order.deliveryStartedAt = new Date();

            // Save the updated order
            await order.save();

            // Update the delivery partner's orders array
            const deliveryPartner = await DeliveryPartner.findById(req.user._id);
            if (deliveryPartner) {
                // Format the delivery address
                let formattedAddress = 'Address not available';
                if (typeof order.deliveryAddress === 'string') {
                    formattedAddress = order.deliveryAddress;
                } else if (order.deliveryAddress && typeof order.deliveryAddress === 'object') {
                    if (order.deliveryAddress.fullAddress) {
                        formattedAddress = order.deliveryAddress.fullAddress;
                    } else {
                        // Construct from parts
                        const parts = [];
                        if (order.deliveryAddress.doorNo) parts.push(order.deliveryAddress.doorNo);
                        if (order.deliveryAddress.streetName) parts.push(order.deliveryAddress.streetName);
                        if (order.deliveryAddress.area) parts.push(order.deliveryAddress.area);
                        if (order.deliveryAddress.district) parts.push(order.deliveryAddress.district);
                        if (order.deliveryAddress.pinCode) parts.push(order.deliveryAddress.pinCode);
                        formattedAddress = parts.join(', ');
                    }
                }

                // Add the order to the delivery partner's orders array
                deliveryPartner.orders.push({
                    orderId: order._id,
                    customerName: order.userId ? (order.userId.name || 'Customer') : 'Customer',
                    deliveryAddress: formattedAddress,
                    deliveryStatus: 'OUT_FOR_DELIVERY',
                    estimatedDeliveryTime: order.expectedDelivery
                });

                // Save the updated delivery partner
                await deliveryPartner.save();
                console.log(`Order ${orderId} assigned to delivery partner ${req.user._id}`);
            }

            // Notify relevant parties
            const io = req.app.get('io');
            if (io) {
                io.to(`USER_${order.userId._id}`).emit('order_status_changed', {
                    orderId: order._id,
                    status: order.status,
                    deliveryPartner: {
                        id: deliveryPartner._id,
                        name: deliveryPartner.name,
                        phoneNumber: deliveryPartner.phoneNumber
                    }
                });
                io.to('admin_room').emit('order_status_changed', {
                    orderId: order._id,
                    status: order.status,
                    deliveryPartner: req.user._id
                });
            }

            return res.status(200).json({
                message: 'Order assigned successfully',
                order: order
            });
        }

        // Handle regular status update case
        // Validate status
        const validStatuses = ['OUT_FOR_DELIVERY', 'DELIVERED'];
        if (!validStatuses.includes(status)) {
            return res.status(400).json({ message: 'Invalid status for delivery partner' });
        }

        // Check if the order has a delivery partner assigned
        if (!order.deliveryPartner) {
            console.log(`Order ${orderId} has no delivery partner assigned. Auto-assigning to current delivery partner.`);

            // Auto-assign the order to this delivery partner
            order.deliveryPartner = req.user._id;
            order.deliveryPartnerAssignedAt = new Date();

            console.log(`Order ${orderId} auto-assigned to delivery partner ${req.user._id}`);
        } else {
            // If the order has a delivery partner, verify it's this delivery partner
            // Convert both IDs to strings for comparison
            const orderDeliveryPartnerId = order.deliveryPartner.toString();
            const requestUserId = req.user._id.toString();

            console.log(`Comparing order delivery partner ID: ${orderDeliveryPartnerId} with request user ID: ${requestUserId}`);

            if (orderDeliveryPartnerId !== requestUserId) {
                console.log(`Unauthorized: Order is assigned to ${orderDeliveryPartnerId}, but request is from ${requestUserId}`);
                return res.status(403).json({
                    message: 'Not authorized to update this order',
                    orderId: orderId,
                    assignedTo: orderDeliveryPartnerId,
                    requestFrom: requestUserId
                });
            }
        }

        // Update the order status
        order.status = status;
        if (status === 'DELIVERED') {
            order.deliveredAt = new Date();
        }

        // Save the updated order
        await order.save();

        // Update the delivery partner's orders array to sync the status
        const deliveryPartner = await DeliveryPartner.findById(req.user._id);
        if (deliveryPartner) {
            // Find the order in the delivery partner's orders array
            const orderIndex = deliveryPartner.orders.findIndex(
                o => o.orderId && o.orderId.toString() === orderId
            );

            if (orderIndex !== -1) {
                // Update the status in the delivery partner's orders array
                deliveryPartner.orders[orderIndex].deliveryStatus = status;
            } else {
                // If the order is not in the delivery partner's orders array, add it
                deliveryPartner.orders.push({
                    orderId: order._id,
                    customerName: order.userId.name || 'Customer',
                    deliveryAddress: typeof order.deliveryAddress === 'string'
                        ? order.deliveryAddress
                        : (order.deliveryAddress.fullAddress || 'Address not available'),
                    deliveryStatus: status,
                    estimatedDeliveryTime: order.expectedDelivery
                });
            }

            // Update delivery partner stats if order is delivered
            if (status === 'DELIVERED') {
                deliveryPartner.totalDeliveries += 1;
                deliveryPartner.completedOrders += 1;
            }

            // Save the updated delivery partner
            await deliveryPartner.save();
        }

        // Send invoice email if order is delivered
        if (status === 'DELIVERED') {
            try {
                // Get the full order with populated user data
                const fullOrder = await Order.findById(orderId).populate('userId', 'name number email');

                if (fullOrder && fullOrder.userId && fullOrder.userId.email) {
                    console.log(`Sending invoice email for delivered order ${fullOrder.orderId} to ${fullOrder.userId.email}`);

                    // Process invoice asynchronously - don't wait for it to complete
                    processOrderInvoice(fullOrder._id)
                        .then(result => {
                            if (result.success) {
                                console.log(`Invoice email sent successfully for order ${fullOrder.orderId}`);
                            } else {
                                console.error(`Failed to send invoice email for order ${fullOrder.orderId}: ${result.error}`);
                            }
                        })
                        .catch(error => {
                            console.error(`Error sending invoice email for order ${fullOrder.orderId}:`, error);
                        });
                } else {
                    console.log(`User for order ${orderId} does not have an email address. Skipping invoice email.`);
                }
            } catch (emailError) {
                // Log error but don't fail the order status update
                console.error('Error sending invoice email:', emailError);
            }
        }

        // Get io instance from app
        const io = req.app.get('io');
        if (io) {
            // Notify relevant parties
            io.to(`USER_${order.userId._id}`).emit('order_status_changed', {
                orderId: order._id,
                status: order.status
            });
            io.to('admin_room').emit('order_status_changed', {
                orderId: order._id,
                status: order.status
            });
        }

        // Send push notifications for status changes
        try {
            const { sendOrderStatusNotification } = require('../utils/pushNotificationUtils');
            const User = require('../models/User');

            // Send notifications for OUT_FOR_DELIVERY and DELIVERED status to users
            if (status === 'OUT_FOR_DELIVERY' || status === 'DELIVERED') {
                // Notify only the customer
                const customer = await User.findById(order.userId._id).select('expoPushToken pushToken name');
                if (customer) {
                    const pushToken = customer.expoPushToken || customer.pushToken;
                    if (pushToken) {
                        console.log(`Sending ${status} notification to user:`, customer.name);
                        await sendOrderStatusNotification(
                            pushToken,
                            order.orderNumber,
                            status,
                            'USER'
                        );
                    } else {
                        console.log('No push token found for user:', customer.name);
                    }
                } else {
                    console.log('Customer not found for order:', order.orderNumber);
                }
            }
        } catch (notificationError) {
            console.error('Error sending delivery status push notifications:', notificationError);
            // Don't fail the status update if notifications fail
        }

        res.status(200).json(order);
    } catch (error) {
        console.error('Error updating order status:', error);
        res.status(500).json({
            message: 'Server error',
            error: error.message
        });
    }
};

/**
 * Update delivery partner availability status
 * @route PUT /api/delivery/availability
 * @access Private (Delivery Partner only)
 */
const updateAvailabilityStatus = async (req, res) => {
    try {
        const { isAvailable } = req.body;

        if (typeof isAvailable !== 'boolean') {
            return res.status(400).json({ message: 'Invalid availability status' });
        }

        const partner = await DeliveryPartner.findByIdAndUpdate(
            req.user._id,
            {
                isAvailable,
                lastActive: new Date()
            },
            { new: true }
        );

        if (!partner) {
            return res.status(404).json({ message: 'Delivery partner not found' });
        }

        res.status(200).json(partner);
    } catch (error) {
        console.error('Error updating availability status:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Get delivery partner profile
 * @route GET /api/delivery/profile
 * @access Private (Delivery Partner only)
 */
const getDeliveryPartnerProfile = async (req, res) => {
    try {
        const partner = await DeliveryPartner.findById(req.user._id)
            .select('-otpCode -otpExpiry -refreshToken');

        if (!partner) {
            return res.status(404).json({ message: 'Delivery partner not found' });
        }

        res.status(200).json(partner);
    } catch (error) {
        console.error('Error fetching delivery partner profile:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Get unassigned orders that could be assigned to a delivery partner
 * @route GET /api/delivery/unassigned-orders
 * @access Private (Delivery Partner only)
 */
const getUnassignedOrders = async (req, res) => {
    try {
        // Find orders that are not assigned to any delivery partner
        // and are in a status that can be assigned (PLACED, CONFIRMED)
        const unassignedOrders = await Order.find({
            deliveryPartner: { $exists: false },
            status: { $in: ['PLACED', 'CONFIRMED'] }
        })
        .populate('userId', 'name number')
        .sort({ createdAt: -1 });

        console.log(`Found ${unassignedOrders.length} unassigned orders`);
        res.status(200).json(unassignedOrders);
    } catch (error) {
        console.error('Error fetching unassigned orders:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Assign an order to the current delivery partner
 * @route PUT /api/delivery/orders/:id/assign
 * @access Private (Delivery Partner only)
 */
const assignOrderToSelf = async (req, res) => {
    try {
        const orderId = req.params.id;

        // Find the order
        const order = await Order.findById(orderId);
        if (!order) {
            return res.status(404).json({ message: 'Order not found' });
        }

        // Check if order is already assigned
        if (order.deliveryPartner) {
            return res.status(400).json({
                message: 'Order is already assigned to a delivery partner'
            });
        }

        // Check if order status is valid for assignment
        if (!['PLACED', 'CONFIRMED'].includes(order.status)) {
            return res.status(400).json({
                message: 'Order cannot be assigned in its current status'
            });
        }

        // Assign the order to this delivery partner
        order.deliveryPartner = req.user._id;
        order.deliveryPartnerAssignedAt = new Date();
        await order.save();

        // Sync with delivery partner's orders array
        await syncOrderWithDeliveryPartner(orderId, req.user._id);

        // Get the updated order with populated fields
        const updatedOrder = await Order.findById(orderId)
            .populate('userId', 'name number')
            .populate('deliveryPartner', 'name phoneNumber');

        res.status(200).json(updatedOrder);
    } catch (error) {
        console.error('Error assigning order:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Sync order with delivery partner
 * This function is used internally to ensure the order is properly added to the delivery partner's orders array
 * @param {string} orderId - The ID of the order
 * @param {string} deliveryPartnerId - The ID of the delivery partner
 */
const syncOrderWithDeliveryPartner = async (orderId, deliveryPartnerId) => {
    try {
        // Get the order and delivery partner
        const order = await Order.findById(orderId).populate('userId', 'name number');
        const deliveryPartner = await DeliveryPartner.findById(deliveryPartnerId);

        if (!order || !deliveryPartner) {
            console.error('Order or delivery partner not found for sync');
            return;
        }

        // Check if the order is already in the delivery partner's orders array
        const orderIndex = deliveryPartner.orders.findIndex(
            o => o.orderId && o.orderId.toString() === orderId.toString()
        );

        // If the order is not in the delivery partner's orders array, add it
        if (orderIndex === -1) {
            // Format the delivery address
            let formattedAddress = 'Address not available';
            if (typeof order.deliveryAddress === 'string') {
                formattedAddress = order.deliveryAddress;
            } else if (order.deliveryAddress && typeof order.deliveryAddress === 'object') {
                if (order.deliveryAddress.fullAddress) {
                    formattedAddress = order.deliveryAddress.fullAddress;
                } else {
                    // Construct from parts
                    const parts = [];
                    if (order.deliveryAddress.doorNo) parts.push(order.deliveryAddress.doorNo);
                    if (order.deliveryAddress.streetName) parts.push(order.deliveryAddress.streetName);
                    if (order.deliveryAddress.area) parts.push(order.deliveryAddress.area);
                    if (order.deliveryAddress.district) parts.push(order.deliveryAddress.district);
                    if (order.deliveryAddress.pinCode) parts.push(order.deliveryAddress.pinCode);

                    formattedAddress = parts.join(', ');
                }
            }

            // Add the order to the delivery partner's orders array
            deliveryPartner.orders.push({
                orderId: order._id,
                customerName: order.userId ? (order.userId.name || 'Customer') : 'Customer',
                deliveryAddress: formattedAddress,
                deliveryStatus: order.status, // Use the same status as the order
                estimatedDeliveryTime: order.expectedDelivery
            });

            // Save the updated delivery partner
            await deliveryPartner.save();
            console.log(`Order ${orderId} synced with delivery partner ${deliveryPartnerId}`);
        } else {
            // Update the status if it's different
            if (deliveryPartner.orders[orderIndex].deliveryStatus !== order.status) {
                deliveryPartner.orders[orderIndex].deliveryStatus = order.status;
                await deliveryPartner.save();
                console.log(`Order ${orderId} status updated in delivery partner ${deliveryPartnerId}`);
            }
        }
    } catch (error) {
        console.error('Error syncing order with delivery partner:', error);
    }
};

module.exports = {
    getDeliveryPartnerOrders,
    updateOrderStatus,
    updateAvailabilityStatus,
    getDeliveryPartnerProfile,
    getUnassignedOrders,
    assignOrderToSelf,
    syncOrderWithDeliveryPartner // Export for use in other controllers
};