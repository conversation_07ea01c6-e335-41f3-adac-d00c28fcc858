<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MeatNow Invoice - Order #<%= typeof orderNumber !== 'undefined' ? orderNumber : orderId %></title>
    <style>
        /* Base styles */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: white;
        }

        /* Container */
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Header */
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #A31621;
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        .header p {
            color: #666;
            margin: 5px 0 0;
        }

        /* Invoice info */
        .invoice-info {
            margin-bottom: 30px;
        }
        .invoice-info table {
            width: 100%;
            border-collapse: collapse;
        }
        .invoice-info td {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .invoice-info tr:last-child td {
            border-bottom: none;
        }
        .invoice-info .label {
            font-weight: 600;
            width: 150px;
            color: #555;
        }

        /* Customer details */
        .customer-details {
            margin-bottom: 30px;
        }
        .customer-details h2 {
            color: #333;
            font-size: 18px;
            margin: 0 0 15px 0;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        .customer-details p {
            margin: 5px 0;
        }
        .customer-details .label {
            font-weight: 600;
            color: #555;
        }

        /* Order items */
        .order-items {
            margin-bottom: 30px;
        }
        .order-items h2 {
            color: #333;
            font-size: 18px;
            margin: 0 0 15px 0;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        .order-items table {
            width: 100%;
            border-collapse: collapse;
        }
        .order-items th {
            text-align: left;
            padding: 10px;
            background-color: #f5f5f5;
            border-bottom: 2px solid #ddd;
            color: #555;
        }
        .order-items td {
            padding: 10px;
            border-bottom: 1px solid #eee;
        }
        .order-items .quantity {
            text-align: center;
        }
        .order-items .price, .order-items .total {
            text-align: right;
        }

        /* Totals */
        .totals {
            margin-bottom: 30px;
        }
        .totals table {
            width: 100%;
            max-width: 400px;
            margin-left: auto;
            border-collapse: collapse;
        }
        .totals td {
            padding: 8px 0;
        }
        .totals .label {
            text-align: left;
        }
        .totals .amount {
            text-align: right;
            font-weight: 600;
        }
        .totals .discount {
            color: #16a34a;
        }
        .totals .grand-total {
            font-size: 16px;
            font-weight: 700;
            border-top: 2px solid #A31621;
            padding-top: 10px;
        }
        .totals .grand-total .amount {
            color: #A31621;
        }

        /* Footer */
        .footer {
            margin-top: 50px;
            text-align: center;
            color: #777;
            font-size: 14px;
            border-top: 1px solid #eee;
            padding-top: 20px;
        }

        /* Red border */
        .red-border {
            border-left: 4px solid #A31621;
            padding-left: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div style="text-align: center; margin-bottom: 15px;">
                <img src="https://res.cloudinary.com/dmjwppr6t/image/upload/v1747812882/logo.png" alt="MeatNow Logo" style="max-width: 150px; height: auto;">
            </div>
            <p>Fresh Meat Delivered to Your Doorstep</p>
        </div>

        <div class="invoice-info">
            <table>
                <tr>
                    <td class="label">Invoice Number:</td>
                    <td><%= invoiceNumber %></td>
                </tr>
                <tr>
                    <td class="label">Order ID:</td>
                    <td><%= typeof orderNumber !== 'undefined' ? orderNumber : orderId %></td>
                </tr>
                <tr>
                    <td class="label">Order Date:</td>
                    <td><%= orderDate %></td>
                </tr>
                <tr>
                    <td class="label">Payment Method:</td>
                    <td><%= paymentMethod.toUpperCase() %></td>
                </tr>
            </table>
        </div>

        <div class="customer-details">
            <h2>Customer Details</h2>
            <p><span class="label">Name:</span> <%= customerName %></p>
            <% if (customerEmail) { %>
            <p><span class="label">Email:</span> <%= customerEmail %></p>
            <% } %>
            <p><span class="label">Phone:</span> <%= customerPhone %></p>
            <p><span class="label">Delivery Address:</span><br>
            <%= deliveryAddress %></p>
        </div>

        <div class="order-items">
            <h2>Order Items</h2>
            <table>
                <thead>
                    <tr>
                        <th>Item</th>
                        <th class="quantity">Qty</th>
                        <th class="price">Price</th>
                        <th class="total">Total</th>
                    </tr>
                </thead>
                <tbody>
                    <% items.forEach(item => { %>
                    <tr>
                        <td><%= item.name %></td>
                        <td class="quantity"><%= item.quantity %></td>
                        <%
                        // Ensure price is defined and valid number
                        const price = typeof item.price === 'number' ? item.price : 0;
                        %>
                        <td class="price">₹<%= price.toFixed(2) %></td>
                        <td class="total">₹<%= (item.quantity * price).toFixed(2) %></td>
                    </tr>
                    <% }); %>
                </tbody>
            </table>
        </div>

        <div class="totals">
            <table>
                <%
                // Calculate the total from the items
                let itemsTotal = 0;
                items.forEach(item => {
                    const price = typeof item.price === 'number' ? item.price : 0;
                    itemsTotal += (item.quantity * price);
                });
                %>
                <tr>
                    <td class="label">Total Amount:</td>
                    <td class="amount">₹<%= itemsTotal.toFixed(2) %></td>
                </tr>
                <%
                // Calculate product discount (difference between items total and original amount)
                const productDiscount = Math.max(0, itemsTotal - originalAmount);
                if (productDiscount > 0) {
                %>
                <tr style="border-bottom: 1px solid #e5e7eb;">
                    <td class="label">Discount on Products:</td>
                    <td class="amount" style="color: #dc2626;">-₹<%= productDiscount.toFixed(2) %></td>
                </tr>
                <% } %>
                <tr>
                    <td class="label">Discounted Amount:</td>
                    <td class="amount">₹<%= originalAmount.toFixed(2) %></td>
                </tr>
                <% if (typeof deliveryFee !== 'undefined' && deliveryFee > 0) { %>
                <tr>
                    <td class="label">Delivery Fee:</td>
                    <td class="amount">₹<%= deliveryFee.toFixed(2) %></td>
                </tr>
                <% } else if (originalAmount >= 499) { %>
                <tr>
                    <td class="label">Delivery Fee:</td>
                    <td class="amount discount">Free (Orders above ₹499)</td>
                </tr>
                <% } %>
                <% if (couponDiscount > 0) { %>
                <tr>
                    <td class="label">Coupon Discount<% if (appliedCoupon) { %> (<%= appliedCoupon %>)<% } %>:</td>
                    <td class="amount discount">-₹<%= couponDiscount.toFixed(2) %></td>
                </tr>
                <% } %>
                <% if (coinsDiscount > 0) { %>
                <tr>
                    <td class="label">Coins Discount:</td>
                    <td class="amount discount">-₹<%= coinsDiscount.toFixed(2) %></td>
                </tr>
                <% } %>
                <%
                // Calculate grand total: Original Amount + Delivery Fee - Coupon Discount - Coins Discount
                const deliveryFeeAmount = typeof deliveryFee !== 'undefined' ? deliveryFee : 0;
                const couponDiscountAmount = typeof couponDiscount === 'number' ? couponDiscount : 0;
                const coinsDiscountAmount = typeof coinsDiscount === 'number' ? coinsDiscount : 0;
                const grandTotal = originalAmount + deliveryFeeAmount - couponDiscountAmount - coinsDiscountAmount;
                %>
                <tr class="grand-total">
                    <td class="label">Grand Total:</td>
                    <td class="amount">₹<%= grandTotal.toFixed(2) %></td>
                </tr>
            </table>
        </div>

        <div class="footer">
            <p>
                MeatNow - Fresh Meat Delivered to Your Doorstep<br>
                Contact: <EMAIL> | +91 8825549901<br>
                © <%= new Date().getFullYear() %> MeatNow. All rights reserved.
            </p>
        </div>
    </div>
</body>
</html>
