const express = require('express');
const router = express.Router();
const { protect } = require('../middleware/authMiddleware');
const User = require('../models/User');
const mongoose = require('mongoose');

/**
 * @route GET /api/user/profile
 * @desc Get user profile
 * @access Private
 */
router.get('/profile', protect, async (req, res) => {
    try {
        const user = await User.findById(req.user._id)
            .select('-otpCode -otpExpiry -refreshToken');

        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        // Ensure address is properly populated
        if (!user.address) {
            user.address = {
                doorNo: "",
                streetName: "",
                area: "",
                district: "",
                pincode: "",
                fullAddress: ""
            };
        }

        // Ensure addresses array is initialized
        if (!user.addresses || !Array.isArray(user.addresses)) {
            user.addresses = [];
        }

        // If user has an address in the address field but no addresses in the array,
        // create an address object from the address field and add it to the array
        if (user.addresses.length === 0 &&
            user.address &&
            (user.address.fullAddress || user.address.doorNo || user.address.streetName)) {

            console.log('Creating address from user profile for /profile endpoint');

            // Create an address object from the user's address field
            const addressFromUserProfile = {
                _id: new mongoose.Types.ObjectId(),
                type: 'Home',
                doorNo: user.address.doorNo || '',
                streetName: user.address.streetName || '',
                area: user.address.area || '',
                district: user.address.district || '',
                pincode: user.address.pincode || '',
                fullAddress: user.address.fullAddress ||
                    `${user.address.doorNo || ''}, ${user.address.streetName || ''}, ${user.address.area || ''}, ${user.address.district || ''}, ${user.address.pincode || ''}`.replace(/\s+/g, ' ').trim(),
                isDefault: true,
                isPrimary: true,
                createdAt: new Date()
            };

            // Add the address to the array
            user.addresses.push(addressFromUserProfile);

            // Save the updated user
            await user.save();
            console.log('Address added to user profile:', addressFromUserProfile);
        }

        // Log the response for debugging
        console.log('Sending user profile with address:', user.address);
        console.log('Sending user profile with addresses array:', user.addresses);

        res.status(200).json({ user });
    } catch (error) {
        console.error('Error fetching user profile:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

/**
 * @route GET /api/user/addresses
 * @desc Get all addresses for the current user
 * @access Private
 */
router.get('/addresses', protect, async (req, res) => {
    try {
        const user = await User.findById(req.user._id);

        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        console.log('Fetching addresses for user:', user._id);

        // Check if user has addresses in the addresses array
        if (user.addresses && Array.isArray(user.addresses) && user.addresses.length > 0) {
            console.log('User has addresses in array:', user.addresses.length);
            return res.status(200).json({ addresses: user.addresses });
        }

        // If no addresses array, but user has an address in the address field, convert it to an array
        if (user.address && (user.address.fullAddress || user.address.doorNo || user.address.streetName)) {
            console.log('Creating address from user profile for /addresses endpoint');

            // Create an address object from the user's address field
            const addressFromUserProfile = {
                _id: new mongoose.Types.ObjectId(),
                type: 'Home',
                doorNo: user.address.doorNo || '',
                streetName: user.address.streetName || '',
                area: user.address.area || '',
                district: user.address.district || '',
                pincode: user.address.pincode || '',
                fullAddress: user.address.fullAddress ||
                    `${user.address.doorNo || ''}, ${user.address.streetName || ''}, ${user.address.area || ''}, ${user.address.district || ''}, ${user.address.pincode || ''}`.replace(/\s+/g, ' ').trim(),
                isDefault: true,
                isPrimary: true,
                createdAt: new Date()
            };

            // Initialize the addresses array with this address
            if (!user.addresses) {
                user.addresses = [];
            }

            user.addresses.push(addressFromUserProfile);
            await user.save();

            console.log('Address created and saved:', addressFromUserProfile);
            return res.status(200).json({ addresses: user.addresses });
        }

        // No addresses found
        console.log('No addresses found for user');
        return res.status(200).json({ addresses: [] });
    } catch (error) {
        console.error('Error fetching user addresses:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

/**
 * @route POST /api/user/addresses
 * @desc Add a new address for the current user
 * @access Private
 */
router.post('/addresses', protect, async (req, res) => {
    try {
        const { type, doorNo, streetName, area, district, pincode, fullAddress, isDefault, isPrimary } = req.body;

        if (!doorNo || !streetName || !area || !district || !pincode) {
            return res.status(400).json({ message: 'All address fields are required' });
        }

        const user = await User.findById(req.user._id);

        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        // Create a new address object
        const newAddress = {
            _id: new mongoose.Types.ObjectId(),
            type: type || 'Home',
            doorNo,
            streetName,
            area,
            district,
            pincode,
            fullAddress: fullAddress || `${doorNo}, ${streetName}, ${area}, ${district}, ${pincode}`,
            isDefault: isDefault || false,
            isPrimary: isPrimary || false,
            createdAt: new Date()
        };

        // Initialize addresses array if it doesn't exist
        if (!user.addresses) {
            user.addresses = [];
        }

        // If this is the default address, update all other addresses
        if (newAddress.isDefault) {
            user.addresses.forEach(addr => {
                addr.isDefault = false;
            });
        }

        // If this is the primary address, update all other addresses and the main address field
        if (newAddress.isPrimary) {
            user.addresses.forEach(addr => {
                addr.isPrimary = false;
            });

            // Update the main address field for backward compatibility
            user.address = {
                doorNo: newAddress.doorNo,
                streetName: newAddress.streetName,
                area: newAddress.area,
                district: newAddress.district,
                pincode: newAddress.pincode,
                fullAddress: newAddress.fullAddress
            };
        }

        // Add the new address
        user.addresses.push(newAddress);

        // Save the user
        await user.save();

        res.status(201).json({ address: newAddress });
    } catch (error) {
        console.error('Error adding user address:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

/**
 * @route PUT /api/user/addresses/:id
 * @desc Update an existing address
 * @access Private
 */
router.put('/addresses/:id', protect, async (req, res) => {
    try {
        const addressId = req.params.id;
        const { type, doorNo, streetName, area, district, pincode, fullAddress, isDefault, isPrimary } = req.body;

        if (!doorNo || !streetName || !area || !district || !pincode) {
            return res.status(400).json({ message: 'All address fields are required' });
        }

        const user = await User.findById(req.user._id);

        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        // Find the address to update
        if (!user.addresses || !Array.isArray(user.addresses)) {
            return res.status(404).json({ message: 'No addresses found for this user' });
        }

        const addressIndex = user.addresses.findIndex(addr =>
            addr._id.toString() === addressId || addr.id === addressId
        );

        if (addressIndex === -1) {
            return res.status(404).json({ message: 'Address not found' });
        }

        // Check if this address was previously primary
        const wasPrimary = user.addresses[addressIndex].isPrimary;

        // Update the address
        const updatedAddress = {
            _id: user.addresses[addressIndex]._id,
            type: type || user.addresses[addressIndex].type,
            doorNo,
            streetName,
            area,
            district,
            pincode,
            fullAddress: fullAddress || `${doorNo}, ${streetName}, ${area}, ${district}, ${pincode}`,
            isDefault: isDefault || false,
            isPrimary: isPrimary !== undefined ? isPrimary : user.addresses[addressIndex].isPrimary,
            createdAt: user.addresses[addressIndex].createdAt || new Date()
        };

        // If this is the default address, update all other addresses
        if (updatedAddress.isDefault) {
            user.addresses.forEach(addr => {
                addr.isDefault = false;
            });
        }

        // If this is being set as the primary address, update all other addresses
        if (updatedAddress.isPrimary && !wasPrimary) {
            user.addresses.forEach(addr => {
                addr.isPrimary = false;
            });

            // Update the main address field for backward compatibility
            user.address = {
                doorNo: updatedAddress.doorNo,
                streetName: updatedAddress.streetName,
                area: updatedAddress.area,
                district: updatedAddress.district,
                pincode: updatedAddress.pincode,
                fullAddress: updatedAddress.fullAddress
            };
        }
        // If this was primary but is no longer primary, update the main address field
        else if (wasPrimary && !updatedAddress.isPrimary) {
            // Find another address to make primary
            const newPrimaryIndex = user.addresses.findIndex((addr, idx) =>
                idx !== addressIndex && addr.isDefault
            );

            if (newPrimaryIndex >= 0) {
                // Make the default address primary
                user.addresses[newPrimaryIndex].isPrimary = true;

                // Update the main address field
                user.address = {
                    doorNo: user.addresses[newPrimaryIndex].doorNo,
                    streetName: user.addresses[newPrimaryIndex].streetName,
                    area: user.addresses[newPrimaryIndex].area,
                    district: user.addresses[newPrimaryIndex].district,
                    pincode: user.addresses[newPrimaryIndex].pincode,
                    fullAddress: user.addresses[newPrimaryIndex].fullAddress
                };
            } else if (user.addresses.length > 1) {
                // Make the first non-current address primary
                const otherIndex = user.addresses.findIndex((addr, idx) => idx !== addressIndex);
                if (otherIndex >= 0) {
                    user.addresses[otherIndex].isPrimary = true;

                    // Update the main address field
                    user.address = {
                        doorNo: user.addresses[otherIndex].doorNo,
                        streetName: user.addresses[otherIndex].streetName,
                        area: user.addresses[otherIndex].area,
                        district: user.addresses[otherIndex].district,
                        pincode: user.addresses[otherIndex].pincode,
                        fullAddress: user.addresses[otherIndex].fullAddress
                    };
                }
            }
        }
        // If this address is still primary, update the main address field
        else if (updatedAddress.isPrimary) {
            user.address = {
                doorNo: updatedAddress.doorNo,
                streetName: updatedAddress.streetName,
                area: updatedAddress.area,
                district: updatedAddress.district,
                pincode: updatedAddress.pincode,
                fullAddress: updatedAddress.fullAddress
            };
        }

        // Replace the old address with the updated one
        user.addresses[addressIndex] = updatedAddress;

        // Save the user
        await user.save();

        res.status(200).json({ address: updatedAddress });
    } catch (error) {
        console.error(`Error updating address ${req.params.id}:`, error);
        res.status(500).json({ message: 'Server error' });
    }
});

/**
 * @route DELETE /api/user/addresses/:id
 * @desc Delete an address
 * @access Private
 */
router.delete('/addresses/:id', protect, async (req, res) => {
    try {
        const addressId = req.params.id;

        const user = await User.findById(req.user._id);

        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        // Find the address to delete
        if (!user.addresses || !Array.isArray(user.addresses)) {
            return res.status(404).json({ message: 'No addresses found for this user' });
        }

        const addressIndex = user.addresses.findIndex(addr =>
            addr._id.toString() === addressId || addr.id === addressId
        );

        if (addressIndex === -1) {
            return res.status(404).json({ message: 'Address not found' });
        }

        // Check if this is the default or primary address
        const isDefault = user.addresses[addressIndex].isDefault;
        const isPrimary = user.addresses[addressIndex].isPrimary;

        // Remove the address
        user.addresses.splice(addressIndex, 1);

        // If there are other addresses
        if (user.addresses.length > 0) {
            // If this was the default address, set the first one as default
            if (isDefault) {
                user.addresses[0].isDefault = true;
            }

            // If this was the primary address, set another one as primary
            if (isPrimary) {
                // First try to find a default address to make primary
                const defaultIndex = user.addresses.findIndex(addr => addr.isDefault);

                if (defaultIndex >= 0) {
                    // Make the default address primary
                    user.addresses[defaultIndex].isPrimary = true;

                    // Update the main address field
                    user.address = {
                        doorNo: user.addresses[defaultIndex].doorNo,
                        streetName: user.addresses[defaultIndex].streetName,
                        area: user.addresses[defaultIndex].area,
                        district: user.addresses[defaultIndex].district,
                        pincode: user.addresses[defaultIndex].pincode,
                        fullAddress: user.addresses[defaultIndex].fullAddress
                    };
                } else {
                    // Otherwise make the first address primary
                    user.addresses[0].isPrimary = true;

                    // Update the main address field
                    user.address = {
                        doorNo: user.addresses[0].doorNo,
                        streetName: user.addresses[0].streetName,
                        area: user.addresses[0].area,
                        district: user.addresses[0].district,
                        pincode: user.addresses[0].pincode,
                        fullAddress: user.addresses[0].fullAddress
                    };
                }
            }
        } else if (isPrimary) {
            // If this was the primary address and there are no other addresses,
            // clear the main address field
            user.address = {
                doorNo: "",
                streetName: "",
                area: "",
                district: "",
                pincode: "",
                fullAddress: ""
            };
        }

        // Save the user
        await user.save();

        res.status(200).json({ message: 'Address deleted successfully' });
    } catch (error) {
        console.error(`Error deleting address ${req.params.id}:`, error);
        res.status(500).json({ message: 'Server error' });
    }
});

/**
 * @route POST /api/user/addresses/set-primary
 * @desc Set an address as the primary address
 * @access Private
 */
router.post('/addresses/set-primary', protect, async (req, res) => {
    try {
        const { addressId } = req.body;

        if (!addressId) {
            return res.status(400).json({ message: 'Address ID is required' });
        }

        const user = await User.findById(req.user._id);

        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        // Find the address
        if (!user.addresses || !Array.isArray(user.addresses)) {
            return res.status(404).json({ message: 'No addresses found for this user' });
        }

        const addressIndex = user.addresses.findIndex(addr =>
            addr._id.toString() === addressId || addr.id === addressId
        );

        if (addressIndex === -1) {
            return res.status(404).json({ message: 'Address not found' });
        }

        // Set all addresses to non-primary
        user.addresses.forEach(addr => {
            addr.isPrimary = false;
        });

        // Set the selected address as primary
        user.addresses[addressIndex].isPrimary = true;

        // Also update the main address field for backward compatibility
        user.address = {
            doorNo: user.addresses[addressIndex].doorNo,
            streetName: user.addresses[addressIndex].streetName,
            area: user.addresses[addressIndex].area,
            district: user.addresses[addressIndex].district,
            pincode: user.addresses[addressIndex].pincode,
            fullAddress: user.addresses[addressIndex].fullAddress
        };

        await user.save();

        res.status(200).json({
            message: 'Primary address updated successfully',
            addresses: user.addresses
        });
    } catch (error) {
        console.error('Error setting primary address:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

/**
 * @route GET /api/user/coins
 * @desc Get user coins/rewards
 * @access Private
 */
router.get('/coins', protect, async (req, res) => {
    try {
        const user = await User.findById(req.user._id);

        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        // Return user coins
        res.status(200).json({ coins: user.coins || [] });
    } catch (error) {
        console.error('Error fetching user coins:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

module.exports = router;
