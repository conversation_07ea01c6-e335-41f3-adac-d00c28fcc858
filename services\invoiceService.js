/**
 * Invoice Service
 *
 * This service handles invoice generation and sending for orders.
 */

const User = require('../models/User');
const Order = require('../models/Order');
const { sendInvoiceEmail } = require('../utils/emailSender');

/**
 * Process invoice for an order
 * @param {string} orderId - ID of the order to process invoice for
 * @returns {Promise<Object>} - Result of the invoice processing
 */
const processOrderInvoice = async (orderId) => {
    try {
        // Find the order with populated user data
        const order = await Order.findById(orderId)
            .populate('userId', 'name number email');

        if (!order) {
            console.error(`Order not found: ${orderId}`);
            return { success: false, error: 'Order not found' };
        }

        // Get user data
        const user = order.userId;

        if (!user || !user.email) {
            console.error(`User email not available for order: ${orderId}`);
            return { success: false, error: 'User email not available' };
        }

        // Log the order data for debugging
        console.log('Order data for invoice:', {
            id: order._id,
            orderId: order.orderId,
            orderNumber: order.orderNumber,
            hasOrderNumber: !!order.orderNumber
        });

        // Log the order data for debugging
        console.log('Order data for invoice:', {
            orderId: order.orderId,
            orderNumber: order.orderNumber,
            originalAmount: order.originalAmount,
            totalAmount: order.totalAmount,
            deliveryFee: order.deliveryFee,
            items: Array.isArray(order.items) ? `${order.items.length} items` : 'No items array'
        });

        // Log delivery fee calculation
        const calculatedDeliveryFee = (order.originalAmount >= 499 || order.totalAmount >= 499) ? 0 : 49;
        console.log('Delivery fee calculation:', {
            originalAmount: order.originalAmount,
            totalAmount: order.totalAmount,
            isFreeDelivery: (order.originalAmount >= 499 || order.totalAmount >= 499),
            calculatedDeliveryFee
        });

        // Validate and prepare items
        const validatedItems = [];
        if (Array.isArray(order.items)) {
            for (const item of order.items) {
                validatedItems.push({
                    name: item.name || 'Unknown Item',
                    quantity: typeof item.quantity === 'number' ? item.quantity : 1,
                    price: typeof item.price === 'number' ? item.price : 0,
                    discount_price: typeof item.discount_price === 'number' ? item.discount_price : null
                });
            }
        }

        // Prepare order data for invoice with thorough validation
        const orderData = {
            orderId: order.orderId || 'unknown-id',
            // Ensure orderNumber is always available, using a fallback if it's not set
            // Convert to string to ensure it's always a valid value for the template
            orderNumber: order.orderNumber ? order.orderNumber.toString() : (order.orderId || 'unknown-id'),
            orderPlacedAt: order.orderPlacedAt || order.createdAt || new Date(),
            customerName: user.name || 'Valued Customer',
            customerEmail: user.email || '<EMAIL>',
            customerPhone: user.number || 'Not provided',
            deliveryAddress: order.deliveryAddress || 'Address not available',
            items: validatedItems,
            originalAmount: typeof order.originalAmount === 'number' ? order.originalAmount : 0,
            totalAmount: typeof order.totalAmount === 'number' ? order.totalAmount : 0,
            couponDiscount: typeof order.couponDiscount === 'number' ? order.couponDiscount : 0,
            coinsDiscount: typeof order.coinsDiscount === 'number' ? order.coinsDiscount : 0,
            coinsEarned: typeof order.coinsEarned === 'number' ? order.coinsEarned : 0,
            paymentMethod: order.paymentMethod || 'COD',
            // Include additional fields for detailed billing
            // Delivery fee is free for orders above 499, otherwise 49
            deliveryFee: typeof order.deliveryFee === 'number' ? order.deliveryFee :
                         ((order.originalAmount >= 499 || order.totalAmount >= 499) ? 0 : 49),
            appliedCoupon: order.appliedCoupon || null,
            // Removed COD fee as per requirement
            codFee: 0
        };

        // Send invoice email
        const emailResult = await sendInvoiceEmail(orderData);

        if (!emailResult.success) {
            console.error(`Failed to send invoice email for order ${orderId}: ${emailResult.error}`);
            return { success: false, error: emailResult.error };
        }

        console.log(`Invoice successfully sent for order ${orderId} to ${user.email}`);
        return { success: true, messageId: emailResult.messageId };
    } catch (error) {
        console.error(`Error processing invoice for order ${orderId}:`, error);
        return { success: false, error: error.message };
    }
};

/**
 * Retry sending invoice for failed attempts
 * @param {string} orderId - ID of the order to retry
 * @returns {Promise<Object>} - Result of the retry operation
 */
const retryInvoiceEmail = async (orderId) => {
    try {
        console.log(`Retrying invoice email for order: ${orderId}`);
        return await processOrderInvoice(orderId);
    } catch (error) {
        console.error(`Error retrying invoice for order ${orderId}:`, error);
        return { success: false, error: error.message };
    }
};

module.exports = {
    processOrderInvoice,
    retryInvoiceEmail
};
