const User = require('../models/User');
const Admin = require('../models/Admin');
const DeliveryPartner = require('../models/DeliveryPartner');
const { createOTP, verifyOTP } = require('../utils/otpUtils');
const { generateAccessToken, generateRefreshToken, verifyToken } = require('../utils/jwtUtils');
const { sendOtpPushNotification, isValidExpoPushToken } = require('../utils/pushNotificationUtils');

/**
 * Store Expo Push Token for a user
 */
const savePushToken = async (req, res) => {
    try {
        const { phoneNumber, expoPushToken } = req.body;

        if (!phoneNumber || !expoPushToken) {
            return res.status(400).json({
                message: 'Phone number and push token are required'
            });
        }

        // Validate push token format
        if (!isValidExpoPushToken(expoPushToken)) {
            return res.status(400).json({
                message: 'Invalid push token format'
            });
        }

        let user;
        let userType = 'USER';

        // Check if it's an admin
        const admin = await Admin.findOne({ phoneNumber });
        if (admin) {
            admin.expoPushToken = expoPushToken;
            admin.pushToken = expoPushToken; // Sync both fields
            await admin.save();
            user = admin;
            userType = 'ADMIN';
        } else {
            // Check if it's a delivery partner
            const deliveryPartner = await DeliveryPartner.findOne({ phoneNumber });
            if (deliveryPartner) {
                deliveryPartner.expoPushToken = expoPushToken;
                deliveryPartner.pushToken = expoPushToken; // Sync both fields
                await deliveryPartner.save();
                user = deliveryPartner;
                userType = 'DELIVERY_PARTNER';
            } else {
                // Regular user - find or create
                user = await User.findOne({ number: phoneNumber });
                if (!user) {
                    // Create new user with push token
                    const generatedEmail = `user${phoneNumber}@gmail.com`;
                    user = new User({
                        name: '',
                        number: phoneNumber,
                        email: generatedEmail,
                        expoPushToken,
                        pushToken: expoPushToken, // Sync both fields
                        coins: 0,
                    });
                } else {
                    user.expoPushToken = expoPushToken;
                    user.pushToken = expoPushToken; // Sync both fields
                }
                await user.save();
            }
        }

        console.log(`Push token saved for ${phoneNumber} (${userType})`);

        res.status(200).json({
            message: 'Push token saved successfully',
            userType
        });
    } catch (error) {
        console.error('Error saving push token:', error);
        res.status(500).json({
            message: 'Internal server error',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
};

/**
 * Send OTP to user's phone number
 * Now uses push notifications when available
 */
const sendOTP = async (req, res) => {
    try {
        const { phoneNumber, name, address } = req.body;

        if (!phoneNumber) {
            return res.status(400).json({ message: 'Phone number is required' });
        }

        // Rate limiting check
        const now = new Date();
        const oneMinuteAgo = new Date(now.getTime() - 60000); // 1 minute ago

        let user;
        let userType = 'USER';

        // Check existing user for rate limiting
        const existingAdmin = await Admin.findOne({ phoneNumber });
        const existingDeliveryPartner = await DeliveryPartner.findOne({ phoneNumber });
        const existingUser = await User.findOne({ number: phoneNumber });

        const existingEntity = existingAdmin || existingDeliveryPartner || existingUser;

        if (existingEntity) {
            // Check rate limiting
            if (existingEntity.lastOtpRequest && existingEntity.lastOtpRequest > oneMinuteAgo) {
                const timeLeft = Math.ceil((existingEntity.lastOtpRequest.getTime() + 60000 - now.getTime()) / 1000);
                return res.status(429).json({
                    message: `Please wait ${timeLeft} seconds before requesting another OTP`,
                    timeLeft
                });
            }

            // Check retry count (max 5 attempts per hour)
            const oneHourAgo = new Date(now.getTime() - 3600000);
            if (existingEntity.lastOtpRequest && existingEntity.lastOtpRequest > oneHourAgo &&
                existingEntity.otpRetryCount >= 5) {
                return res.status(429).json({
                    message: 'Too many OTP requests. Please try again after an hour.',
                    retryAfter: 3600
                });
            }

            // Reset retry count if more than an hour has passed
            if (!existingEntity.lastOtpRequest || existingEntity.lastOtpRequest <= oneHourAgo) {
                existingEntity.otpRetryCount = 0;
            }
        }

        // Generate OTP with 10 minutes expiry
        const { otpCode, otpExpiry } = createOTP(10);

        let isNewUser = false;
        // user and userType are already declared above

        // First check if it's an admin
        const admin = await Admin.findOne({ phoneNumber });
        if (admin) {
            // Update admin with new OTP
            admin.otpCode = otpCode;
            admin.otpExpiry = otpExpiry;
            await admin.save();
            user = admin;
            userType = 'ADMIN';
        }
        // Then check if it's a delivery partner
        else {
            const deliveryPartner = await DeliveryPartner.findOne({ phoneNumber });
            if (deliveryPartner) {
                // Update delivery partner with new OTP
                deliveryPartner.otpCode = otpCode;
                deliveryPartner.otpExpiry = otpExpiry;
                await deliveryPartner.save();
                user = deliveryPartner;
                userType = 'DELIVERY_PARTNER';
            }
            // Finally check if it's a regular user
            else {
                user = await User.findOne({ number: phoneNumber });

                if (!user) {
                    console.log(`Creating new user with phone number: ${phoneNumber}`);

                    // For new users, create a new user record
                    // Generate an email using the phone number
                    const generatedEmail = `user${phoneNumber}@gmail.com`;
                    console.log(`Generated email: ${generatedEmail}`);

                    if (name && address) {
                        console.log(`Creating user with name: ${name} and address: ${address}`);
                        user = new User({
                            name,
                            number: phoneNumber,
                            address,
                            email: generatedEmail, // Set the generated email
                            otpCode,
                            otpExpiry,
                            coins: 0, // Explicitly set coins to 0
                        });
                    } else {
                        console.log('Creating user with empty name');
                        user = new User({
                            name: '',
                            number: phoneNumber,
                            email: generatedEmail, // Set the generated email
                            otpCode,
                            otpExpiry,
                            coins: 0, // Explicitly set coins to 0
                        });
                    }
                    isNewUser = true;

                    try {
                        console.log('Saving new user to database...');
                        await user.save();
                        console.log('User saved successfully:', user);
                    } catch (saveError) {
                        console.error('Error saving new user:', saveError);
                        throw saveError; // Re-throw to be caught by the outer catch block
                    }
                } else {
                    console.log(`Updating existing user with phone number: ${phoneNumber}`);
                    // Update existing user with new OTP
                    user.otpCode = otpCode;
                    user.otpExpiry = otpExpiry;

                    try {
                        console.log('Saving updated user to database...');
                        await user.save();
                        console.log('User updated successfully');
                    } catch (saveError) {
                        console.error('Error updating user:', saveError);
                        throw saveError; // Re-throw to be caught by the outer catch block
                    }
                }
            }
        }

        // Send OTP via push notification if token is available
        let pushNotificationSent = false;
        if (user.expoPushToken) {
            console.log(`Attempting to send OTP via push notification to ${phoneNumber}`);
            pushNotificationSent = await sendOtpPushNotification(user.expoPushToken, otpCode, phoneNumber);
        }

        // Log OTP for development/fallback
        console.log(`OTP for ${phoneNumber}: ${otpCode}`);

        // Update retry count and last request time
        user.otpRetryCount = (user.otpRetryCount || 0) + 1;
        user.lastOtpRequest = new Date();
        await user.save();

        res.status(200).json({
            message: pushNotificationSent ? 'OTP sent via notification' : 'OTP generated successfully',
            pushNotificationSent,
            // For development purposes only, remove in production
            otp: process.env.NODE_ENV === 'development' ? otpCode : undefined,
            userType,
            isNewUser,
            ...(isNewUser && { requiresProfile: !name || !address })
        });
    } catch (error) {
        console.error('Error sending OTP:', error);

        // Provide more detailed error information
        if (error.name === 'ValidationError') {
            // Mongoose validation error
            const errors = {};
            for (const field in error.errors) {
                errors[field] = error.errors[field].message;
            }
            console.error('Validation errors:', errors);
            return res.status(400).json({
                message: 'Validation error',
                errors
            });
        } else if (error.name === 'MongoServerError' && error.code === 11000) {
            // Duplicate key error
            console.error('Duplicate key error:', error.keyValue);

            // Special handling for email duplicate key error
            if (error.keyValue && error.keyValue.email === null) {
                console.log('Null email duplicate key error - this is a known issue');

                // Get the phone number from the request body
                const reqPhoneNumber = req.body.phoneNumber;

                if (reqPhoneNumber) {
                    // Try to find the user by phone number instead
                    try {
                        const existingUser = await User.findOne({ number: reqPhoneNumber });

                        if (existingUser) {
                            console.log('Found existing user by phone number:', existingUser);

                            // Generate a new OTP
                            const { otpCode: newOtpCode, otpExpiry: newOtpExpiry } = createOTP();

                            // Update the existing user with the new OTP
                            existingUser.otpCode = newOtpCode;
                            existingUser.otpExpiry = newOtpExpiry;

                            await existingUser.save();

                            // Return success response
                            return res.status(200).json({
                                message: 'OTP sent successfully',
                                otp: newOtpCode, // For development only, remove in production
                                isNewUser: false
                            });
                        } else {
                            console.log('No user found with phone number:', reqPhoneNumber);
                        }
                    } catch (findError) {
                        console.error('Error finding user by phone number:', findError);
                    }
                } else {
                    console.log('No phone number provided in request body');
                }
            }

            return res.status(400).json({
                message: 'Duplicate key error',
                field: Object.keys(error.keyValue)[0]
            });
        }

        // Generic server error
        res.status(500).json({
            message: 'Server error',
            error: error.message
        });
    }
};

/**
 * Verify OTP and authenticate user
 */
const verifyOTPAndLogin = async (req, res) => {
    try {
        const { phoneNumber, otp } = req.body;

        if (!phoneNumber || !otp) {
            return res.status(400).json({ message: 'Phone number and OTP are required' });
        }

        let user;
        let userType = 'USER'; // Default user type
        let tokenPayload = {};

        // First check if it's an admin
        user = await Admin.findOne({ phoneNumber });
        if (user) {
            userType = 'ADMIN';
            console.log('Admin user found:', user);
            tokenPayload = {
                id: user._id,
                userType: 'ADMIN',
                role: 'ADMIN'
            };
        }
        // Then check if it's a delivery partner
        else {
            user = await DeliveryPartner.findOne({ phoneNumber });
            if (user) {
                userType = 'DELIVERY_PARTNER';
                console.log('Delivery partner found:', user);
                tokenPayload = {
                    id: user._id,
                    userType: 'DELIVERY_PARTNER'
                };
            }
            // Finally check if it's a regular user
            else {
                user = await User.findOne({ number: phoneNumber });
                if (user) {
                    userType = 'USER';
                    console.log('Regular user found:', user);
                    tokenPayload = {
                        id: user._id,
                        userType: 'USER',
                        isNewUser: !user.name // Check if user is new (name not set)
                    };
                }
            }
        }

        console.log('User type determined:', userType);
        console.log('Token payload:', tokenPayload);

        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        // Verify OTP
        const isValidOTP = verifyOTP(otp, user.otpCode, user.otpExpiry);

        if (!isValidOTP) {
            return res.status(400).json({ message: 'Invalid or expired OTP' });
        }

        // Clear OTP and reset retry count after successful verification
        user.otpCode = null;
        user.otpExpiry = null;
        user.otpRetryCount = 0;
        user.lastOtpRequest = null;
        await user.save();

        // Generate JWT access token
        const accessToken = generateAccessToken(tokenPayload);

        // Generate refresh token with longer expiry
        const refreshToken = generateRefreshToken({ id: user._id, userType });

        // Save refresh token to user
        user.refreshToken = refreshToken;
        await user.save();

        // Set cookies for tokens (more secure than localStorage)
        res.cookie('refreshToken', refreshToken, {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
            sameSite: 'strict'
        });

        // Prepare user response data with consistent userType and role
        const userData = {
            id: user._id,
            name: user.name,
            phoneNumber: userType === 'USER' ? user.number : user.phoneNumber,
            userType: userType,
            role: userType === 'ADMIN' ? 'ADMIN' : undefined,
            ...(userType === 'USER' && { isNewUser: !user.name })
        };

        console.log('Sending user data in response:', userData);

        // Return tokens and user data
        res.status(200).json({
            message: 'Authentication successful',
            token: accessToken,
            refreshToken, // Include in response for mobile apps
            user: userData
        });
    } catch (error) {
        console.error('Error verifying OTP:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Update user profile
 * @route POST /api/auth/update-profile
 * @access Private
 */
const updateUserProfile = async (req, res) => {
    try {
        const { name, email, address } = req.body;
        const userId = req.user._id; // Get user ID from auth middleware

        if (!name) {
            return res.status(400).json({ message: 'Name is required' });
        }

        // Find user based on user type
        let user;

        if (req.userType === 'USER') {
            user = await User.findById(userId);

            if (!user) {
                return res.status(404).json({ message: 'User not found' });
            }

            // Update user profile
            user.name = name;
            if (email) {
                user.email = email;
            }

            // Handle address updates with the new model
            if (address) {
                console.log('Updating address with data:', address);

                // Update the legacy address field for backward compatibility
                user.address = {
                    doorNo: address.doorNo || '',
                    streetName: address.streetName || '',
                    area: address.area || '',
                    district: address.district || '',
                    pincode: address.pincode || '',
                    fullAddress: address.fullAddress || '',
                    coordinates: {
                        latitude: address.coordinates?.latitude || address.latitude || null,
                        longitude: address.coordinates?.longitude || address.longitude || null
                    },
                    latitude: address.coordinates?.latitude || address.latitude || null,
                    longitude: address.coordinates?.longitude || address.longitude || null,
                    addressType: address.addressType || 'Home'
                };

                // Generate fullAddress if not provided
                const fullAddress = address.fullAddress ||
                    `${address.doorNo || ''}, ${address.streetName || ''}, ${address.area || ''}, ${address.district || ''}, ${address.pincode || ''}`.replace(/\s+/g, ' ').trim();

                console.log('Generated fullAddress:', fullAddress);

                // Initialize addresses array if it doesn't exist
                if (!user.addresses) {
                    console.log('Initializing addresses array');
                    user.addresses = [];
                }

                // First check if there's an existing primary address
                const primaryIndex = user.addresses.findIndex(addr => addr.isPrimary);
                console.log('Found primary address index:', primaryIndex);

                // Then check if there's an address with the same details (even if not primary)
                const matchingIndex = user.addresses.findIndex(addr =>
                    addr.doorNo === address.doorNo &&
                    addr.streetName === address.streetName &&
                    addr.area === address.area &&
                    addr.district === address.district &&
                    addr.pincode === address.pincode
                );
                console.log('Found matching address index:', matchingIndex);

                // If we have a primary address, update it
                if (primaryIndex >= 0) {
                    console.log('Updating existing primary address:', user.addresses[primaryIndex]);
                    // Update existing primary address
                    user.addresses[primaryIndex].doorNo = address.doorNo || user.addresses[primaryIndex].doorNo;
                    user.addresses[primaryIndex].streetName = address.streetName || user.addresses[primaryIndex].streetName;
                    user.addresses[primaryIndex].area = address.area || user.addresses[primaryIndex].area;
                    user.addresses[primaryIndex].district = address.district || user.addresses[primaryIndex].district;
                    user.addresses[primaryIndex].pincode = address.pincode || user.addresses[primaryIndex].pincode;
                    user.addresses[primaryIndex].fullAddress = fullAddress;

                    // Update coordinates in both formats
                    if (address.coordinates || address.latitude || address.longitude) {
                        // Update nested coordinates
                        if (!user.addresses[primaryIndex].coordinates) {
                            user.addresses[primaryIndex].coordinates = {};
                        }
                        user.addresses[primaryIndex].coordinates.latitude = address.coordinates?.latitude || address.latitude || user.addresses[primaryIndex].coordinates?.latitude;
                        user.addresses[primaryIndex].coordinates.longitude = address.coordinates?.longitude || address.longitude || user.addresses[primaryIndex].coordinates?.longitude;

                        // Update flat coordinates for backward compatibility
                        user.addresses[primaryIndex].latitude = address.coordinates?.latitude || address.latitude || user.addresses[primaryIndex].latitude;
                        user.addresses[primaryIndex].longitude = address.coordinates?.longitude || address.longitude || user.addresses[primaryIndex].longitude;
                    }

                    // Update address type if provided
                    if (address.addressType) {
                        user.addresses[primaryIndex].addressType = address.addressType;
                        user.addresses[primaryIndex].type = address.addressType;
                    }
                    console.log('Updated primary address:', user.addresses[primaryIndex]);
                }
                // If we have a matching address but it's not primary, make it primary
                else if (matchingIndex >= 0) {
                    console.log('Found matching address, updating it to be primary:', user.addresses[matchingIndex]);

                    // Set all addresses to non-primary
                    user.addresses.forEach(addr => {
                        addr.isPrimary = false;
                    });

                    // Update the matching address
                    user.addresses[matchingIndex].doorNo = address.doorNo || user.addresses[matchingIndex].doorNo;
                    user.addresses[matchingIndex].streetName = address.streetName || user.addresses[matchingIndex].streetName;
                    user.addresses[matchingIndex].area = address.area || user.addresses[matchingIndex].area;
                    user.addresses[matchingIndex].district = address.district || user.addresses[matchingIndex].district;
                    user.addresses[matchingIndex].pincode = address.pincode || user.addresses[matchingIndex].pincode;
                    user.addresses[matchingIndex].fullAddress = fullAddress;
                    user.addresses[matchingIndex].isPrimary = true;

                    // Update coordinates in both formats
                    if (address.coordinates || address.latitude || address.longitude) {
                        // Update nested coordinates
                        if (!user.addresses[matchingIndex].coordinates) {
                            user.addresses[matchingIndex].coordinates = {};
                        }
                        user.addresses[matchingIndex].coordinates.latitude = address.coordinates?.latitude || address.latitude || user.addresses[matchingIndex].coordinates?.latitude;
                        user.addresses[matchingIndex].coordinates.longitude = address.coordinates?.longitude || address.longitude || user.addresses[matchingIndex].coordinates?.longitude;

                        // Update flat coordinates for backward compatibility
                        user.addresses[matchingIndex].latitude = address.coordinates?.latitude || address.latitude || user.addresses[matchingIndex].latitude;
                        user.addresses[matchingIndex].longitude = address.coordinates?.longitude || address.longitude || user.addresses[matchingIndex].longitude;
                    }

                    // Update address type if provided
                    if (address.addressType) {
                        user.addresses[matchingIndex].addressType = address.addressType;
                        user.addresses[matchingIndex].type = address.addressType;
                    }
                    console.log('Updated matching address to be primary:', user.addresses[matchingIndex]);
                }
                // If no primary or matching address, create a new one
                else {
                    console.log('No primary or matching address found, creating new primary address');

                    // Set all existing addresses to non-primary
                    user.addresses.forEach(addr => {
                        addr.isPrimary = false;
                    });

                    // Create the new primary address
                    const newPrimaryAddress = {
                        type: address.addressType || 'Home',
                        doorNo: address.doorNo || '',
                        streetName: address.streetName || '',
                        area: address.area || '',
                        district: address.district || '',
                        pincode: address.pincode || '',
                        fullAddress: fullAddress,
                        // Handle coordinates in both formats
                        coordinates: {
                            latitude: address.coordinates?.latitude || address.latitude || null,
                            longitude: address.coordinates?.longitude || address.longitude || null
                        },
                        // For backward compatibility
                        latitude: address.coordinates?.latitude || address.latitude || null,
                        longitude: address.coordinates?.longitude || address.longitude || null,
                        isPrimary: true,
                        isDefault: false,
                        createdAt: new Date(),
                        addressType: address.addressType || 'Home',
                        isWithinDeliveryZone: address.isWithinDeliveryZone || true
                    };

                    console.log('Creating new primary address:', newPrimaryAddress);
                    user.addresses.push(newPrimaryAddress);
                }
            }
        } else if (req.userType === 'ADMIN') {
            user = await Admin.findById(userId);

            if (!user) {
                return res.status(404).json({ message: 'Admin not found' });
            }

            // Update admin profile
            user.name = name;
            if (email) {
                user.email = email;
            }
        } else if (req.userType === 'DELIVERY_PARTNER') {
            user = await DeliveryPartner.findById(userId);

            if (!user) {
                return res.status(404).json({ message: 'Delivery partner not found' });
            }

            // Update delivery partner profile
            user.name = name;
            if (email) {
                user.email = email;
            }
        } else {
            return res.status(400).json({ message: 'Invalid user type' });
        }

        await user.save();

        // Prepare response based on user type
        let userResponse;

        if (req.userType === 'USER') {
            // Calculate total coins based on the storage format
            let totalCoins = 0;

            if (user.coins !== undefined) {
                if (Array.isArray(user.coins)) {
                    // If coins is an array of objects with amount property
                    totalCoins = user.coins.reduce((sum, coin) => sum + (coin.amount || 0), 0);
                    console.log('Calculated total coins from array:', totalCoins);
                } else if (typeof user.coins === 'number') {
                    // If coins is a simple number
                    totalCoins = user.coins;
                    console.log('Using coins as number:', totalCoins);
                }
            }

            userResponse = {
                id: user._id,
                name: user.name,
                phoneNumber: user.number,
                email: user.email,
                address: user.address,
                addresses: user.addresses, // Include addresses array in response
                coins: user.coins, // Include raw coins data
                totalCoins: totalCoins, // Include calculated total coins
                userType: 'USER'
            };
        } else if (req.userType === 'ADMIN') {
            userResponse = {
                id: user._id,
                name: user.name,
                phoneNumber: user.phoneNumber,
                email: user.email,
                userType: 'ADMIN'
            };
        } else {
            userResponse = {
                id: user._id,
                name: user.name,
                phoneNumber: user.phoneNumber,
                email: user.email,
                vehicleType: user.vehicleType,
                vehicleNumber: user.vehicleNumber,
                userType: 'DELIVERY_PARTNER'
            };
        }

        res.status(200).json({
            message: 'Profile updated successfully',
            user: userResponse
        });
    } catch (error) {
        console.error('Error updating profile:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Add a new delivery partner (Admin only)
 */
const addDeliveryPartner = async (req, res) => {
    try {
        const { name, phoneNumber, email, vehicleType, vehicleNumber, location } = req.body;
        const adminId = req.user.id; // From auth middleware

        if (!name || !phoneNumber) {
            return res.status(400).json({ message: 'Name and phone number are required' });
        }

        // Check if delivery partner already exists
        const existingPartner = await DeliveryPartner.findOne({ phoneNumber });

        if (existingPartner) {
            return res.status(400).json({ message: 'Delivery partner with this phone number already exists' });
        }

        // Create new delivery partner
        const newPartner = new DeliveryPartner({
            name,
            phoneNumber,
            email,
            vehicleType: vehicleType || 'Two Wheeler',
            vehicleNumber,
            location,
            addedBy: adminId,
            joinedDate: new Date()
        });

        await newPartner.save();

        res.status(201).json({
            message: 'Delivery partner added successfully',
            partner: {
                id: newPartner._id,
                name: newPartner.name,
                phoneNumber: newPartner.phoneNumber,
                vehicleType: newPartner.vehicleType
            }
        });
    } catch (error) {
        console.error('Error adding delivery partner:', error);
        res.status(500).json({ message: 'Server error' });
    }
};



/**
 * Check if user exists and determine user type
 */
const checkUserExists = async (req, res) => {
    try {
        const { phoneNumber } = req.body;

        if (!phoneNumber) {
            return res.status(400).json({ message: 'Phone number is required' });
        }

        // First check if it's an admin
        const admin = await Admin.findOne({ phoneNumber });
        if (admin) {
            return res.status(200).json({
                exists: true,
                userType: 'ADMIN',
                user: {
                    id: admin._id,
                    name: admin.name,
                    phoneNumber: admin.phoneNumber,
                    role: admin.role
                }
            });
        }

        // Then check if it's a delivery partner
        const deliveryPartner = await DeliveryPartner.findOne({ phoneNumber });
        if (deliveryPartner) {
            return res.status(200).json({
                exists: true,
                userType: 'DELIVERY_PARTNER',
                user: {
                    id: deliveryPartner._id,
                    name: deliveryPartner.name,
                    phoneNumber: deliveryPartner.phoneNumber
                }
            });
        }

        // Finally check if it's a regular user
        const user = await User.findOne({ number: phoneNumber });
        if (user) {
            return res.status(200).json({
                exists: true,
                userType: 'USER',
                user: {
                    id: user._id,
                    name: user.name,
                    number: user.number,
                    address: user.address
                }
            });
        }

        // User doesn't exist in any collection
        return res.status(200).json({
            exists: false,
            userType: 'USER', // Default to USER for new registrations
            message: 'User not found. Please register.'
        });

    } catch (error) {
        console.error('Error checking user:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Refresh access token using refresh token
 * @route POST /api/auth/refresh-token
 * @access Public
 */
const refreshToken = async (req, res) => {
    try {
        const { refreshToken } = req.body;

        if (!refreshToken) {
            return res.status(400).json({ message: 'Refresh token is required' });
        }

        // Verify refresh token
        const decoded = verifyToken(refreshToken, true);
        if (!decoded) {
            return res.status(401).json({ message: 'Invalid refresh token' });
        }

        // Find user based on user type
        let user;

        switch (decoded.userType) {
            case 'USER':
                user = await User.findById(decoded.id);
                break;
            case 'ADMIN':
                user = await Admin.findById(decoded.id);
                break;
            case 'DELIVERY_PARTNER':
                user = await DeliveryPartner.findById(decoded.id);
                break;
            default:
                return res.status(400).json({ message: 'Invalid user type' });
        }

        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        // Check if refresh token matches the one stored in the database
        if (user.refreshToken !== refreshToken) {
            return res.status(401).json({ message: 'Refresh token is invalid' });
        }

        // Generate new access token
        const tokenPayload = {
            id: user._id,
            userType: decoded.userType
        };

        // Add additional fields based on user type
        if (decoded.userType === 'ADMIN' && user.role) {
            tokenPayload.role = user.role;
        }

        const accessToken = generateAccessToken(tokenPayload);

        res.status(200).json({
            message: 'Token refreshed successfully',
            token: accessToken
        });
    } catch (error) {
        console.error('Error refreshing token:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Logout user
 * @route POST /api/auth/logout
 * @access Private
 */
const logout = async (req, res) => {
    try {
        const userId = req.user._id;
        const userType = req.userType;

        // Find user based on user type
        let user;

        switch (userType) {
            case 'USER':
                user = await User.findById(userId);
                break;
            case 'ADMIN':
                user = await Admin.findById(userId);
                break;
            case 'DELIVERY_PARTNER':
                user = await DeliveryPartner.findById(userId);
                break;
            default:
                return res.status(400).json({ message: 'Invalid user type' });
        }

        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        // Clear refresh token
        user.refreshToken = null;
        await user.save();

        // Clear cookies
        res.clearCookie('refreshToken');

        res.status(200).json({ message: 'Logged out successfully' });
    } catch (error) {
        console.error('Error logging out:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

module.exports = {
    savePushToken,
    sendOTP,
    verifyOTPAndLogin,
    updateUserProfile,
    addDeliveryPartner,
    checkUserExists,
    refreshToken,
    logout
};