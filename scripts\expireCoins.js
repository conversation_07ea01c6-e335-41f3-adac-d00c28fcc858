/**
 * <PERSON><PERSON>t to expire coins that have passed their expiry date
 *
 * This script:
 * 1. Finds all users with 'EARNED' coins that have expired
 * 2. Adds corresponding 'EXPIRED' entries to their coinsHistory
 * 3. Updates the simple coins counter
 *
 * Run this script periodically (e.g., daily) using a cron job or scheduler
 */

const mongoose = require('mongoose');
const User = require('../models/User');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI, {
    useNewUrlParser: true,
    useUnifiedTopology: true
})
.then(() => console.log('MongoDB connected for coin expiry check'))
.catch(err => {
    console.error('MongoDB connection error:', err);
    process.exit(1);
});

const expireCoins = async () => {
    const now = new Date();
    console.log(`Starting coin expiry check at ${now.toISOString()}`);

    let session;
    try {
        // Start a session for transaction
        session = await mongoose.startSession();
        session.startTransaction();

        // Find all users with coins
        const users = await User.find({
            'coinsHistory.type': 'EARNED',
            'coinsHistory.expiry': { $lte: now }
        }).session(session);

        console.log(`Found ${users.length} users with potentially expired coins`);

        let totalExpiredCoins = 0;
        let usersUpdated = 0;

        for (const user of users) {
            // Filter for expired 'EARNED' coins that haven't been marked as expired yet
            const expiredCoins = user.coinsHistory.filter(coin =>
                coin.type === 'EARNED' &&
                coin.expiry &&
                new Date(coin.expiry) <= now
            );

            if (expiredCoins.length === 0) {
                continue; // Skip if no expired coins
            }

            // Create 'EXPIRED' entries for each expired coin
            const expiredEntries = [];
            let totalExpired = 0;

            for (const coin of expiredCoins) {
                // Create a new 'EXPIRED' entry
                expiredEntries.push({
                    amount: -coin.amount, // Negative amount for 'EXPIRED'
                    type: 'EXPIRED',
                    orderId: coin.orderId, // Reference the original order
                    orderNumber: coin.orderNumber,
                    date: now,
                    description: `Expired coins originally earned from Order #${coin.orderNumber || 'Unknown'}`
                });

                totalExpired += coin.amount;
            }

            if (totalExpired > 0) {
                // Update user document
                await User.updateOne(
                    { _id: user._id },
                    {
                        $push: { coinsHistory: { $each: expiredEntries } },
                        $inc: { coins: -totalExpired } // Decrement the simple counter
                    },
                    { session }
                );

                console.log(`Expired ${totalExpired} coins for user ${user._id} (${user.name || 'unnamed'})`);
                totalExpiredCoins += totalExpired;
                usersUpdated++;
            }
        }

        // Commit the transaction
        await session.commitTransaction();
        session.endSession();

        console.log(`Coin expiry process completed successfully`);
        console.log(`Total expired coins: ${totalExpiredCoins}`);
        console.log(`Users updated: ${usersUpdated}`);

    } catch (error) {
        console.error('Error in coin expiry process:', error);

        // If a session was started, abort the transaction
        if (session) {
            await session.abortTransaction();
            session.endSession();
        }
    } finally {
        // Disconnect from MongoDB
        mongoose.disconnect();
        console.log('MongoDB disconnected after coin expiry check');
    }
};

// Execute the function
expireCoins();
