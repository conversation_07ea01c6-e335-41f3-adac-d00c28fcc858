const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();
console.log('MongoDB URI:', process.env.MONGO_URI ? 'URI is set' : 'URI is not set');

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI)
    .then(async () => {
        console.log('MongoDB Connected');
        
        try {
            // Get the User collection
            const db = mongoose.connection.db;
            const userCollection = db.collection('users');
            
            // Get all indexes on the User collection
            const indexes = await userCollection.indexes();
            console.log('Current indexes:', indexes);
            
            // Find the email index
            const emailIndex = indexes.find(index => 
                index.key && index.key.email !== undefined
            );
            
            if (emailIndex) {
                console.log('Found email index:', emailIndex);
                
                // Drop the email index
                await userCollection.dropIndex(emailIndex.name);
                console.log('Dropped email index:', emailIndex.name);
                
                // Verify the index was dropped
                const updatedIndexes = await userCollection.indexes();
                console.log('Updated indexes:', updatedIndexes);
            } else {
                console.log('No email index found');
            }
            
            console.log('Index operation completed');
            process.exit(0);
        } catch (error) {
            console.error('Error dropping email index:', error);
            process.exit(1);
        }
    })
    .catch(err => {
        console.error('MongoDB connection error:', err);
        process.exit(1);
    });
