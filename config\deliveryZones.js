/**
 * Delivery Zone Configuration
 *
 * This file defines the delivery zones for the application.
 * It includes valid pincodes and geographical boundaries.
 */

// List of valid pincodes for delivery - Vellore District Only
const validPincodes = [
    // Vellore District - Specific Areas Only
    '632515', // Tiruvalam, Ponnai Koot Road (Tiruvalam)
    '632519', // Ammundi, Karnambut, Sugarmill, EB Kut Road Tiruvalam
    '632106', // Sevur, Arumbaruthi
    '632014', // VIT, Brammapuram
    '632001', // Vellore Main
    '632006'  // Old Katpadi, Chittoor Bus Stand (Katpadi)
];

// Central points of delivery operations
const centralPoints = [
    {
        name: 'Chennai',
        latitude: 13.0827,
        longitude: 80.2707
    },
    {
        name: 'Vellore',
        latitude: 12.9165,
        longitude: 79.1325
    }
];

// Use Chennai as the default central point for backward compatibility
const centralPoint = centralPoints[0];

// Maximum delivery radius in kilometers
const maxDeliveryRadius = 15;

// Polygon-based delivery zones (for more precise boundary definition)
const deliveryZonePolygons = [
    // Chennai city center area
    [
        { latitude: 13.0569, longitude: 80.2425 }, // Southwest corner
        { latitude: 13.0569, longitude: 80.2989 }, // Southeast corner
        { latitude: 13.1085, longitude: 80.2989 }, // Northeast corner
        { latitude: 13.1085, longitude: 80.2425 }  // Northwest corner
    ],
    // Vellore city area
    [
        { latitude: 12.8970, longitude: 79.1160 }, // Southwest corner
        { latitude: 12.8970, longitude: 79.1560 }, // Southeast corner
        { latitude: 12.9370, longitude: 79.1560 }, // Northeast corner
        { latitude: 12.9370, longitude: 79.1160 }  // Northwest corner
    ]
];

// Use Chennai polygon as the default for backward compatibility
const deliveryZonePolygon = deliveryZonePolygons[0];

// Time slots configuration
const deliveryTimeSlots = {
    morning: {
        start: 9, // 9 AM
        end: 12   // 12 PM
    },
    evening: {
        start: 17, // 5 PM
        end: 19    // 7 PM
    }
};

module.exports = {
    validPincodes,
    centralPoint,
    centralPoints,
    maxDeliveryRadius,
    deliveryZonePolygon,
    deliveryZonePolygons,
    deliveryTimeSlots
};
