/**
 * Cleanup script to remove duplicate addresses
 * Run this script with: node backend/scripts/cleanupDuplicateAddresses.js
 */

const mongoose = require('mongoose');
const User = require('../models/User');
require('dotenv').config();

// Connect to MongoDB with direct URI
const MONGO_URI = 'mongodb+srv://MeatHub:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';

mongoose.connect(MONGO_URI, {
    useNewUrlParser: true,
    useUnifiedTopology: true
})
.then(() => console.log('MongoDB connected for address cleanup'))
.catch(err => {
    console.error('MongoDB connection error:', err);
    process.exit(1);
});

// Function to check if two addresses are duplicates
const areDuplicateAddresses = (addr1, addr2) => {
    return addr1.doorNo === addr2.doorNo &&
           addr1.streetName === addr2.streetName &&
           addr1.area === addr2.area &&
           addr1.district === addr2.district &&
           addr1.pincode === addr2.pincode;
};

async function cleanupDuplicateAddresses() {
    try {
        console.log('Starting duplicate address cleanup...');
        
        // Find all users
        const users = await User.find({}).lean();
        
        console.log(`Found ${users.length} users to check for duplicate addresses`);
        
        let cleanedCount = 0;
        let unchangedCount = 0;
        let errorCount = 0;
        
        for (const user of users) {
            try {
                // Skip users without addresses array
                if (!user.addresses || !Array.isArray(user.addresses) || user.addresses.length <= 1) {
                    console.log(`User ${user._id} has 0 or 1 addresses, skipping`);
                    unchangedCount++;
                    continue;
                }
                
                console.log(`Checking addresses for user: ${user._id} (${user.name || 'unnamed'})`);
                console.log(`User has ${user.addresses.length} addresses`);
                
                // Find the primary address
                const primaryAddress = user.addresses.find(addr => addr.isPrimary);
                
                if (!primaryAddress) {
                    console.log(`User ${user._id} has no primary address, skipping`);
                    unchangedCount++;
                    continue;
                }
                
                // Find duplicates of the primary address
                const duplicates = user.addresses.filter(addr => 
                    addr._id.toString() !== primaryAddress._id.toString() && 
                    areDuplicateAddresses(addr, primaryAddress)
                );
                
                if (duplicates.length === 0) {
                    console.log(`No duplicates found for user ${user._id}`);
                    unchangedCount++;
                    continue;
                }
                
                console.log(`Found ${duplicates.length} duplicates of the primary address for user ${user._id}`);
                
                // Get the IDs of duplicates to remove
                const duplicateIds = duplicates.map(addr => addr._id);
                
                // Remove the duplicates
                await User.updateOne(
                    { _id: user._id },
                    { $pull: { addresses: { _id: { $in: duplicateIds } } } }
                );
                
                console.log(`Removed ${duplicateIds.length} duplicate addresses for user ${user._id}`);
                cleanedCount++;
            } catch (error) {
                console.error(`Error cleaning up addresses for user ${user._id}:`, error);
                errorCount++;
            }
        }
        
        console.log('\nCleanup Summary:');
        console.log(`- ${cleanedCount} users had duplicate addresses removed`);
        console.log(`- ${unchangedCount} users had no duplicate addresses`);
        console.log(`- ${errorCount} users encountered errors during cleanup`);
        console.log('\nCleanup completed!');
        
    } catch (error) {
        console.error('Error during cleanup:', error);
    } finally {
        // Close the MongoDB connection
        mongoose.connection.close();
        console.log('MongoDB connection closed');
    }
}

// Run the cleanup
cleanupDuplicateAddresses();
