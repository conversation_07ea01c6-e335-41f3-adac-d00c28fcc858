const mongoose = require("mongoose");

const userSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: false, // Allow empty name for new users
      default: '',
    },
    number: {
      type: String,
      required: true,
      unique: true,
      trim: true, // Remove whitespace
    },
    email: {
      type: String,
      required: false, // Made optional
      unique: false, // Removed unique constraint
      default: null, // Changed back to null
    },
    otpCode: {
      type: String,
      default: null,
    },
    otpExpiry: {
      type: Date,
      default: null,
    },
    // Updated address structure with 5 sections and location coordinates
    address: {
      doorNo: {
        type: String,
        default: "",
      },
      streetName: {
        type: String,
        default: "",
      },
      area: {
        type: String,
        default: "",
      },
      district: {
        type: String,
        default: "",
      },
      pincode: {
        type: String,
        default: "",
      },
      // Keep the full address string for backward compatibility
      fullAddress: {
        type: String,
        default: "",
      },
      // Location coordinates
      coordinates: {
        latitude: {
          type: Number,
          default: null,
        },
        longitude: {
          type: Number,
          default: null,
        }
      },
      // For backward compatibility
      latitude: {
        type: Number,
        default: null,
      },
      longitude: {
        type: Number,
        default: null,
      },
      // Address type
      addressType: {
        type: String,
        enum: ['Home', 'Work', 'Other'],
        default: 'Home'
      }
    },
    // Array of addresses for multiple address support
    addresses: [
      {
        _id: { type: mongoose.Schema.Types.ObjectId, auto: true },
        type: { type: String, default: "Home" },
        doorNo: { type: String, required: true },
        streetName: { type: String, required: true },
        area: { type: String, required: true },
        district: { type: String, required: true },
        pincode: { type: String, required: true },
        fullAddress: { type: String, required: true },
        // Enhanced location coordinates
        coordinates: {
          latitude: { type: Number, default: null },
          longitude: { type: Number, default: null }
        },
        // For backward compatibility
        latitude: { type: Number, default: null },
        longitude: { type: Number, default: null },
        isDefault: { type: Boolean, default: false },
        isPrimary: { type: Boolean, default: false },
        createdAt: { type: Date, default: Date.now },
        isWithinDeliveryZone: { type: Boolean, default: true },
        addressType: { type: String, enum: ['Home', 'Work', 'Other'], default: 'Home' }
      }
    ],
    // Simple coin counter (for backward compatibility)
    coins: { type: Number, default: 0 },
    usedCoins: { type: Number, default: 0 }, // Track used coins separately

    // Enhanced coin tracking with detailed information
    coinsHistory: [
      {
        amount: { type: Number, required: true },
        type: { type: String, enum: ['EARNED', 'USED', 'EXPIRED', 'REFUNDED'], required: true },
        orderId: { type: mongoose.Schema.Types.ObjectId, ref: 'Order' },
        orderNumber: { type: String },
        date: { type: Date, default: Date.now },
        expiry: { type: Date },
        description: { type: String }
      }
    ],

    // Last coin use tracking
    lastCoinUse: {
      amount: { type: Number, default: 0 },
      orderId: { type: String, default: null },
      date: { type: Date, default: null }
    },

    // Coupon usage tracking
    usedCoupons: [
      {
        couponCode: { type: String, required: true },
        orderId: { type: mongoose.Schema.Types.ObjectId, ref: 'Order' },
        orderNumber: { type: String },
        usedAt: { type: Date, default: Date.now },
        discountAmount: { type: Number, default: 0 }
      }
    ],

    orderHistory: [
      {
        orderId: { type: mongoose.Schema.Types.ObjectId, ref: "Order" },
        date: { type: Date, default: Date.now },
      },
    ],
    isAdmin: {
      type: Boolean,
      required: true,
      default: false,
    },
    refreshToken: {
      type: String,
      default: null,
    },
    expoPushToken: {
      type: String,
      default: null,
    },
    // Alias for expoPushToken for consistency
    pushToken: {
      type: String,
      default: null,
    },
    otpRetryCount: {
      type: Number,
      default: 0,
    },
    lastOtpRequest: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

const User = mongoose.model("User", userSchema);
module.exports = User;
