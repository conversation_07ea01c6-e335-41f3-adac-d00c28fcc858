const mongoose = require('mongoose');
const dotenv = require('dotenv');
const DeliveryPartner = require('../models/DeliveryPartner');
const Admin = require('../models/Admin');

// Load environment variables
dotenv.config();
console.log('MongoDB URI:', process.env.MONGO_URI ? 'URI is set' : 'URI is not set');

// Delivery partner data
const deliveryPartnerData = {
    name: 'Delivery Partner',
    phoneNumber: '9894258293', // Delivery partner phone number
    email: '<EMAIL>',
    vehicleType: 'Two Wheeler',
    vehicleNumber: 'TN 01 AB 1234',
    isActive: true,
    isAvailable: true
};

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI)
    .then(async () => {
        console.log('MongoDB Connected');
        
        try {
            // Check if delivery partner already exists
            const existingPartner = await DeliveryPartner.findOne({ phoneNumber: deliveryPartnerData.phoneNumber });
            
            if (existingPartner) {
                console.log('Delivery partner already exists with phone number 9894258293:', existingPartner);
                process.exit(0);
            }
            
            // Find an admin to set as addedBy
            const admin = await Admin.findOne();
            
            if (admin) {
                deliveryPartnerData.addedBy = admin._id;
            }
            
            // Create new delivery partner
            const partner = new DeliveryPartner(deliveryPartnerData);
            const savedPartner = await partner.save();
            console.log('Delivery partner created with phone number 9894258293:', savedPartner);
            
            process.exit(0);
        } catch (error) {
            console.error('Error:', error);
            process.exit(1);
        }
    })
    .catch(err => {
        console.error('MongoDB connection error:', err);
        process.exit(1);
    });
