/**
 * <PERSON><PERSON><PERSON> to set up cron jobs for the application
 * 
 * This script sets up scheduled tasks including:
 * - Daily coin expiry check
 * 
 * Run this script when the server starts to schedule all necessary tasks
 */

const cron = require('node-cron');
const path = require('path');
const { exec } = require('child_process');

console.log('Setting up cron jobs...');

// Schedule coin expiry check to run daily at midnight
cron.schedule('0 0 * * *', () => {
    console.log('Running scheduled coin expiry check...');
    
    const scriptPath = path.join(__dirname, 'expireCoins.js');
    
    exec(`node ${scriptPath}`, (error, stdout, stderr) => {
        if (error) {
            console.error(`<PERSON>rror executing coin expiry script: ${error.message}`);
            return;
        }
        
        if (stderr) {
            console.error(`Coin expiry script stderr: ${stderr}`);
        }
        
        console.log(`Coin expiry script output: ${stdout}`);
    });
});

console.log('Cron jobs set up successfully');

// Export a function to initialize cron jobs from the main server file
module.exports = {
    initCronJobs: () => {
        console.log('Cron jobs initialized');
    }
};
