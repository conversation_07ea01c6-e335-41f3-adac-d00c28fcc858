const Category = require('../models/Category');

/**
 * Get all categories
 * @route GET /api/categories
 * @access Public
 */
const getAllCategories = async (req, res) => {
    try {
        const categories = await Category.find().populate('products');
        res.status(200).json(categories);
    } catch (error) {
        console.error('Error fetching categories:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Get category by ID
 * @route GET /api/categories/:id
 * @access Public
 */
const getCategoryById = async (req, res) => {
    try {
        const category = await Category.findById(req.params.id).populate('products');
        
        if (!category) {
            return res.status(404).json({ message: 'Category not found' });
        }
        
        res.status(200).json(category);
    } catch (error) {
        console.error('Error fetching category:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Create new category
 * @route POST /api/categories
 * @access Private (Admin only)
 */
const createCategory = async (req, res) => {
    try {
        const { name, image } = req.body;
        
        if (!name || !image) {
            return res.status(400).json({ message: 'Name and image are required' });
        }
        
        // Generate a unique ID
        const categoryCount = await Category.countDocuments();
        const id = `CAT${(categoryCount + 1).toString().padStart(3, '0')}`;
        
        // Create new category
        const newCategory = new Category({
            id,
            name,
            image,
            products: []
        });
        
        const savedCategory = await newCategory.save();
        
        res.status(201).json(savedCategory);
    } catch (error) {
        console.error('Error creating category:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Update category
 * @route PUT /api/categories/:id
 * @access Private (Admin only)
 */
const updateCategory = async (req, res) => {
    try {
        const { name, image } = req.body;
        
        // Check if category exists
        const category = await Category.findById(req.params.id);
        if (!category) {
            return res.status(404).json({ message: 'Category not found' });
        }
        
        // Update category
        const updatedCategory = await Category.findByIdAndUpdate(
            req.params.id,
            {
                name: name || category.name,
                image: image || category.image
            },
            { new: true }
        ).populate('products');
        
        res.status(200).json(updatedCategory);
    } catch (error) {
        console.error('Error updating category:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Delete category
 * @route DELETE /api/categories/:id
 * @access Private (Admin only)
 */
const deleteCategory = async (req, res) => {
    try {
        // Check if category exists
        const category = await Category.findById(req.params.id);
        if (!category) {
            return res.status(404).json({ message: 'Category not found' });
        }
        
        // Check if category has products
        if (category.products.length > 0) {
            return res.status(400).json({ message: 'Cannot delete category with products' });
        }
        
        // Delete category
        await Category.findByIdAndDelete(req.params.id);
        
        res.status(200).json({ message: 'Category deleted successfully' });
    } catch (error) {
        console.error('Error deleting category:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

module.exports = {
    getAllCategories,
    getCategoryById,
    createCategory,
    updateCategory,
    deleteCategory
};
