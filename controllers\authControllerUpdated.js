// This is a partial update to the authController.js file
// Only the updateUserProfile function is shown here

/**
 * Update user profile
 * @route POST /api/auth/update-profile
 * @access Private
 */
const updateUserProfile = async (req, res) => {
    try {
        const { name, email, address } = req.body;
        const userId = req.user._id; // Get user ID from auth middleware

        if (!name) {
            return res.status(400).json({ message: 'Name is required' });
        }

        let user;

        if (req.userType === 'USER') {
            user = await User.findById(userId);

            if (!user) {
                return res.status(404).json({ message: 'User not found' });
            }

            // Update user profile
            user.name = name;
            
            if (email) {
                user.email = email;
            }
            
            // Handle address update with new structure
            if (address) {
                if (typeof address === 'string') {
                    // Handle legacy format (string)
                    user.address = {
                        fullAddress: address,
                        doorNo: '',
                        streetName: '',
                        area: '',
                        district: '',
                        pincode: ''
                    };
                } else if (typeof address === 'object') {
                    // Handle new format (object with 5 sections)
                    user.address = {
                        doorNo: address.doorNo || '',
                        streetName: address.streetName || '',
                        area: address.area || '',
                        district: address.district || '',
                        pincode: address.pincode || '',
                        // Generate fullAddress from components for backward compatibility
                        fullAddress: address.fullAddress || 
                            `${address.doorNo || ''}, ${address.streetName || ''}, ${address.area || ''}, ${address.district || ''}, ${address.pincode || ''}`
                    };
                }
            }
        } else if (req.userType === 'ADMIN') {
            user = await Admin.findById(userId);

            if (!user) {
                return res.status(404).json({ message: 'Admin not found' });
            }

            // Update admin profile
            user.name = name;
            if (email) {
                user.email = email;
            }
        } else if (req.userType === 'DELIVERY_PARTNER') {
            user = await DeliveryPartner.findById(userId);

            if (!user) {
                return res.status(404).json({ message: 'Delivery partner not found' });
            }

            // Update delivery partner profile
            user.name = name;
            if (email) {
                user.email = email;
            }
        } else {
            return res.status(400).json({ message: 'Invalid user type' });
        }

        await user.save();

        // Prepare response based on user type
        let userResponse;

        if (req.userType === 'USER') {
            userResponse = {
                id: user._id,
                name: user.name,
                phoneNumber: user.number,
                email: user.email,
                address: user.address,
                userType: 'USER'
            };
        } else if (req.userType === 'ADMIN') {
            userResponse = {
                id: user._id,
                name: user.name,
                phoneNumber: user.phoneNumber,
                email: user.email,
                userType: 'ADMIN'
            };
        } else {
            userResponse = {
                id: user._id,
                name: user.name,
                phoneNumber: user.phoneNumber,
                email: user.email,
                vehicleType: user.vehicleType,
                vehicleNumber: user.vehicleNumber,
                userType: 'DELIVERY_PARTNER'
            };
        }

        res.status(200).json({
            message: 'Profile updated successfully',
            user: userResponse
        });
    } catch (error) {
        console.error('Error updating profile:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
