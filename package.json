{"name": "backend", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "dev": "nodemon server.js"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"axios": "^1.9.0", "bcryptjs": "^3.0.2", "colors": "^1.4.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.7", "ejs": "^3.1.10", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "firebase-admin": "^13.4.0", "html-pdf": "^3.0.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.13.0", "node-cron": "^3.0.3", "nodemailer": "^7.0.3", "nodemon": "^3.1.9", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "uuid": "^11.1.0"}}