/**
 * Email Sender Utility
 *
 * This utility handles sending emails with attachments using nodemailer.
 */

const nodemailer = require('nodemailer');
const fs = require('fs');
const path = require('path');
const ejs = require('ejs');
const emailConfig = require('../config/emailConfig');
const { generateInvoicePdf } = require('./pdfGenerator');

// Create email transporter
const createTransporter = () => {
    return nodemailer.createTransport({
        service: emailConfig.service,
        auth: emailConfig.auth
    });
};

/**
 * Send invoice email to customer
 * @param {Object} orderData - Order data including customer email and order details
 * @returns {Promise<Object>} - Result of the email sending operation
 */
const sendInvoiceEmail = async (orderData) => {
    try {
        // Validate required data
        if (!orderData || !orderData.customerEmail) {
            console.error('Missing required data for sending invoice email');
            return { success: false, error: 'Missing required data' };
        }

        // Ensure orderNumber is always available as a string
        const orderIdentifier = orderData.orderNumber ? orderData.orderNumber.toString() : orderData.orderId;
        console.log(`Preparing to send invoice email for order ${orderIdentifier} to ${orderData.customerEmail}`);

        // Generate PDF invoice
        const pdfPath = await generateInvoicePdf(orderData);

        // Create email transporter
        const transporter = createTransporter();

        // Render email template
        const templatePath = path.join(__dirname, '..', 'templates', 'emails', 'orderDelivered.ejs');
        const emailHtml = await ejs.renderFile(templatePath, {
            orderNumber: orderIdentifier,
            orderId: orderData.orderId,
            orderDate: new Date(orderData.orderPlacedAt).toLocaleDateString('en-IN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            }),
            customerName: orderData.customerName,
            customerEmail: orderData.customerEmail,
            customerPhone: orderData.customerPhone,
            deliveryAddress: typeof orderData.deliveryAddress === 'string'
                ? orderData.deliveryAddress
                : formatAddress(orderData.deliveryAddress),
            items: (orderData.items || []).map(item => ({
                ...item,
                // Ensure all required properties are defined and valid
                name: item.name || 'Unknown Item',
                quantity: typeof item.quantity === 'number' ? item.quantity : 1,
                price: typeof item.price === 'number' ? item.price : 0,
                discount_price: typeof item.discount_price === 'number' ? item.discount_price : null
            })),
            originalAmount: orderData.originalAmount || 0,
            totalAmount: orderData.totalAmount || 0,
            couponDiscount: orderData.couponDiscount || 0,
            coinsDiscount: orderData.coinsDiscount || 0,
            coinsEarned: orderData.coinsEarned || 0,
            paymentMethod: orderData.paymentMethod || 'COD',
            deliveryFee: orderData.deliveryFee || 0,
            appliedCoupon: orderData.appliedCoupon || null
        });

        // Configure email with PDF attachment
        const mailOptions = {
            from: `"${emailConfig.from.name}" <${emailConfig.from.email}>`,
            to: orderData.customerEmail,
            subject: `Your Meat Now Order #${orderIdentifier} was Sucessfully Delivered!`,
            html: emailHtml,
            // Keep a plain text version for email clients that don't support HTML
            text: `Dear ${orderData.customerName},

Thank you for your order with Meat Now!

Your order #${orderIdentifier} has been delivered. Please find your invoice attached to this email.

Order Details:
- Order Number: ${orderIdentifier}
- Order Date: ${new Date(orderData.orderPlacedAt).toLocaleDateString('en-IN')}
- Total Amount: ₹${orderData.totalAmount.toFixed(2)}
- Payment Method: ${orderData.paymentMethod.toUpperCase()}

If you have any questions about your order, please contact our customer <NAME_EMAIL> or call us at +91 8825549901.

Thank you for choosing Meat Now!

Regards,
The Meat Now Team`,
            attachments: [
                {
                    filename: `Meat Now_Invoice_${orderIdentifier}.pdf`,
                    path: pdfPath,
                    contentType: 'application/pdf'
                }
            ]
        };

        // Send email
        const info = await transporter.sendMail(mailOptions);
        console.log(`Invoice email sent to ${orderData.customerEmail} for order ${orderIdentifier}`);
        console.log('Email info:', info.messageId);

        // Clean up - delete PDF after sending
        try {
            fs.unlinkSync(pdfPath);
            console.log(`Deleted temporary PDF file: ${pdfPath}`);
        } catch (unlinkError) {
            console.error('Error deleting temporary PDF file:', unlinkError);
            // Continue even if cleanup fails
        }

        return { success: true, messageId: info.messageId };
    } catch (error) {
        console.error('Error sending invoice email:', error);
        return { success: false, error: error.message };
    }
};

/**
 * Format address object into a string
 * @param {Object} address - Address object
 * @returns {string} - Formatted address string
 */
const formatAddress = (address) => {
    if (!address) return 'Address not available';

    // If address has fullAddress property, use it
    if (address.fullAddress) return address.fullAddress;

    // Otherwise, construct from components
    const components = [];

    if (address.doorNo) components.push(address.doorNo);
    if (address.streetName) components.push(address.streetName);
    if (address.area) components.push(address.area);
    if (address.district || address.city) components.push(address.district || address.city);
    if (address.pincode) components.push(address.pincode);

    return components.join(', ');
};

module.exports = {
    sendInvoiceEmail
};
