/**
 * Utility functions for OTP generation and verification
 */

/**
 * Generate a random OTP of specified length
 * @param {number} length - Length of OTP (default: 6)
 * @returns {string} - Generated OTP
 */
const generateOTP = (length = 6) => {
    const digits = '0123456789';
    let OTP = '';
    
    for (let i = 0; i < length; i++) {
        OTP += digits[Math.floor(Math.random() * 10)];
    }
    
    return OTP;
};

/**
 * Create an OTP with expiry time
 * @param {number} expiryMinutes - Minutes until OTP expires (default: 10)
 * @returns {Object} - Object containing OTP and expiry time
 */
const createOTP = (expiryMinutes = 10) => {
    const otp = generateOTP();
    const expiryTime = new Date();
    expiryTime.setMinutes(expiryTime.getMinutes() + expiryMinutes);
    
    return {
        otpCode: otp,
        otpExpiry: expiryTime
    };
};

/**
 * Verify if OTP is valid and not expired
 * @param {string} inputOTP - OTP entered by user
 * @param {string} storedOTP - OTP stored in database
 * @param {Date} expiryTime - Expiry time of OTP
 * @returns {boolean} - True if OTP is valid and not expired
 */
const verifyOTP = (inputOTP, storedOTP, expiryTime) => {
    if (!inputOTP || !storedOTP || !expiryTime) {
        return false;
    }
    
    const now = new Date();
    
    // Check if OTP is expired
    if (now > expiryTime) {
        return false;
    }
    
    // Check if OTP matches
    return inputOTP === storedOTP;
};

module.exports = {
    generateOTP,
    createOTP,
    verifyOTP
};
