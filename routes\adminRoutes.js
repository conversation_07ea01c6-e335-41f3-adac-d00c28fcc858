const express = require('express');
const router = express.Router();
const { protect, adminOnly } = require('../middleware/authMiddleware');
const DeliveryPartner = require('../models/DeliveryPartner');
const {
    getAllUsers,
    getAllDeliveryPartners,
    addDeliveryPartner,
    updateDeliveryPartner,
    toggleDeliveryPartnerAvailability,
    deleteDeliveryPartner,
    getAdminOrders,
    getOrderById,
    getOrdersByStatus,
    getOrdersByUserId,
    getOrdersByDeliveryPartnerId,
    updateOrderStatus,
    getAdminProducts,
    updateProduct,
    deleteProduct,
    createProduct,
    getDashboardAnalytics
} = require('../controllers/adminController');

// All routes are protected and admin-only
router.use(protect, adminOnly);

// User management
router.get('/users', getAllUsers);

// Delivery partner management
router.get('/delivery-partners', getAllDeliveryPartners);
router.get('/delivery-partners/:id', async (req, res) => {
    try {
        console.log('Getting delivery partner with ID:', req.params.id);

        let partner;

        // Check if the ID is a MongoDB ObjectId or a string ID like "DP001"
        if (req.params.id.startsWith('DP')) {
            // Find by custom ID field
            console.log('Looking for delivery partner with custom ID:', req.params.id);
            partner = await DeliveryPartner.findOne({ id: req.params.id });
        } else {
            try {
                // Try to find by MongoDB ObjectId
                console.log('Using MongoDB ObjectId to find partner');
                partner = await DeliveryPartner.findById(req.params.id);
            } catch (idError) {
                console.error('Error finding by _id:', idError.message);
                return res.status(404).json({ message: 'Delivery partner not found with ID: ' + req.params.id });
            }
        }

        if (!partner) {
            return res.status(404).json({ message: 'Delivery partner not found with ID: ' + req.params.id });
        }

        console.log('Found delivery partner:', partner.name);
        res.status(200).json({ deliveryPartner: partner });
    } catch (error) {
        console.error('Error getting delivery partner:', error);
        console.error('Error details:', error.message);
        res.status(500).json({ message: 'Server error: ' + error.message });
    }
});
router.post('/delivery-partners', addDeliveryPartner);
router.put('/delivery-partners/:id', updateDeliveryPartner);
router.patch('/delivery-partners/:id/toggle-availability', toggleDeliveryPartnerAvailability);
router.delete('/delivery-partners/:id', deleteDeliveryPartner);

// Order management
router.get('/orders', getAdminOrders);
router.get('/orders/status/:status', getOrdersByStatus);
router.get('/orders/user/:userId', getOrdersByUserId);
router.get('/orders/delivery-partner/:partnerId', getOrdersByDeliveryPartnerId);
router.get('/orders/:id', getOrderById);
router.patch('/orders/:id/status', updateOrderStatus);

// Product management
router.get('/products', getAdminProducts);

// Get product by ID - inline implementation
router.get('/products/:id', async (req, res) => {
  try {
    const Product = require('../models/Product');
    const mongoose = require('mongoose');

    const productId = req.params.id;
    console.log('Admin route: Fetching product with ID:', productId);

    let product = null;

    // Try multiple approaches to find the product

    // 1. First try by MongoDB ObjectId
    try {
      if (mongoose.Types.ObjectId.isValid(productId)) {
        console.log('Looking for product with MongoDB ObjectId');
        product = await Product.findById(productId).populate('category');
        if (product) {
          console.log('Found product by MongoDB ObjectId:', product.name);
        }
      }
    } catch (idError) {
      console.error('Error finding by MongoDB ObjectId:', idError.message);
    }

    // 2. If not found, try by custom ID field
    if (!product) {
      console.log('Looking for product with custom ID field');
      product = await Product.findOne({ id: productId }).populate('category');
      if (product) {
        console.log('Found product by custom ID field:', product.name);
      }
    }

    // If product is still not found, return 404
    if (!product) {
      console.log('Product not found with ID:', productId);
      return res.status(404).json({
        message: 'Product not found with ID: ' + productId
      });
    }

    console.log('Successfully found product:', product.name);
    res.status(200).json({ product: product });
  } catch (error) {
    console.error('Error fetching product:', error);
    res.status(500).json({
      message: 'Server error',
      error: error.message
    });
  }
});

router.post('/products', createProduct);
router.put('/products/:id', updateProduct);
router.delete('/products/:id', deleteProduct);

// Analytics
router.get('/analytics/dashboard', getDashboardAnalytics);

module.exports = router;