const mongoose = require("mongoose");

const connectDB = async () => {
    try {
        // Set mongoose options
        const options = {
            useNewUrlParser: true,
            useUnifiedTopology: true,
            serverSelectionTimeoutMS: 30000, // 30 seconds timeout for server selection
            connectTimeoutMS: 30000, // 30 seconds timeout for initial connection
            socketTimeoutMS: 45000, // 45 seconds timeout for socket operations
            family: 4, // Use IPv4, skip trying IPv6
            retryWrites: true,
            w: 'majority'
        };

        console.log('Connecting to MongoDB Atlas...');
        const conn = await mongoose.connect(process.env.MONGO_URI, options);
        console.log(`MongoDB Connected: ${conn.connection.host}`);
        console.log('Connection state:', mongoose.connection.readyState);

        // Test the connection by listing collections
        const collections = await mongoose.connection.db.listCollections().toArray();
        console.log('Available collections:', collections.map(c => c.name).join(', '));

        return conn;
    } catch (error) {
        console.error('MongoDB Connection Error:');
        console.error(`Error message: ${error.message}`);
        console.error('Full error:', error);

        // Don't exit the process in development
        if (process.env.NODE_ENV === 'production') {
            process.exit(1);
        }

        return null;
    }
};

module.exports = connectDB;
