/**
 * Email Configuration
 * 
 * This file contains configuration for email services used in the application.
 * For production, use environment variables to store sensitive information.
 */

// Load environment variables
require('dotenv').config();

// Email configuration
const emailConfig = {
    // Email service configuration (Gmail)
    service: process.env.EMAIL_SERVICE || 'gmail',
    
    // Email authentication
    auth: {
        user: process.env.EMAIL_USER || '<EMAIL>',
        pass: process.env.EMAIL_PASSWORD || 'your-app-password', // Use app-specific password
    },
    
    // Default sender information
    from: {
        name: process.env.EMAIL_FROM_NAME || 'Meat Now',
        email: process.env.EMAIL_FROM_ADDRESS || '<EMAIL>',
    },
    
    // Email templates directory
    templatesDir: process.env.EMAIL_TEMPLATES_DIR || 'templates/emails',
    
    // Invoice settings
    invoice: {
        directory: process.env.INVOICE_DIR || 'invoices',
        prefix: process.env.INVOICE_PREFIX || 'INV-',
    }
};

module.exports = emailConfig;
