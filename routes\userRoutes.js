const express = require('express');
const router = express.Router();
const { protect } = require('../middleware/authMiddleware');
const {
    getUserProfile,
    updateUserProfile,
    getUserCoins,
    getUserOrders
} = require('../controllers/userController');
const User = require('../models/User');

// Get user profile (using controller)
router.get('/profile', protect, getUserProfile);

// Update user profile
router.put('/profile', protect, updateUserProfile);

// Get user's coin history
router.get('/coins', protect, getUserCoins);

// Get user's order history
router.get('/orders', protect, getUserOrders);

// Get all users (for testing only, should be admin-only in production)
router.get('/', async (req, res) => {
    try {
        const users = await User.find()
            .select('-otpCode -otpExpiry -refreshToken');

        console.log('Users found:', users.length);
        res.status(200).json({ users });
    } catch (error) {
        console.error('Error fetching users:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

// Get user by ID
router.get('/:id', async (req, res) => {
    try {
        const user = await User.findById(req.params.id)
            .select('-otpCode -otpExpiry -refreshToken');

        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        res.status(200).json({ user });
    } catch (error) {
        console.error(`Error fetching user ${req.params.id}:`, error);
        res.status(500).json({ message: 'Server error' });
    }
});

module.exports = router;