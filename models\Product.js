const mongoose = require("mongoose");

const productSchema = new mongoose.Schema(
  {
    id: { type: String, required: true, unique: true },
    name: { type: String, required: true },
    description: { type: String },
    price: { type: Number, required: true },
    discount_price: { type: Number },
    discountPercentage: { type: Number, default: 0 },
    weight: { type: String },
    pieces: { type: String },
    available: { type: Boolean, default: true },
    isAvailable: { type: Boolean, default: true },
    stock: { type: Number, default: 0 },
    offer: { type: String },
    reward_points: { type: Number, default: 0 },
    category: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Category", // Linking to Category schema
    },
    image: { type: String, required: true }, // Image URL for product
    sold: { type: Number, default: 0 }
  },
  { timestamps: true }
);

const Product = mongoose.model("Product", productSchema);
module.exports = Product;
