const mongoose = require('mongoose');
const User = require('../models/User');
const DeliveryPartner = require('../models/DeliveryPartner');
const Order = require('../models/Order');
const Product = require('../models/Product');
const { processOrderInvoice } = require('../services/invoiceService');

/**
 * Get all users
 * @route GET /api/admin/users
 * @access Private (Admin only)
 */
const getAllUsers = async (req, res) => {
    try {
        const users = await User.find({ isAdmin: false })
            .select('-otpCode -otpExpiry -refreshToken')
            .sort({ createdAt: -1 });
        console.log('Users found:', users.length);
        res.status(200).json({ users: users });
    } catch (error) {
        console.error('Error fetching users:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Get all delivery partners
 * @route GET /api/admin/delivery-partners
 * @access Private (Admin only)
 */
const getAllDeliveryPartners = async (req, res) => {
    try {
        const partners = await DeliveryPartner.find()
            .select('-otpCode -otpExpiry -refreshToken')
            .sort({ createdAt: -1 });

        console.log('Delivery partners found:', partners.length);

        // Ensure each partner has an id field
        const partnersWithId = partners.map(partner => {
            const partnerObj = partner.toObject();

            // If no custom id exists, generate one based on the MongoDB _id
            if (!partnerObj.id) {
                partnerObj.id = `DP${partnerObj._id.toString().slice(-3).padStart(3, '0')}`;
            }

            return partnerObj;
        });

        res.status(200).json({ deliveryPartners: partnersWithId });
    } catch (error) {
        console.error('Error fetching delivery partners:', error);
        console.error('Error details:', error.message);
        res.status(500).json({ message: 'Server error: ' + error.message });
    }
};

/**
 * Add new delivery partner
 * @route POST /api/admin/delivery-partners
 * @access Private (Admin only)
 */
const addDeliveryPartner = async (req, res) => {
    try {
        const { name, phoneNumber, email, vehicleType, vehicleNumber, id } = req.body;

        // Check if partner already exists
        const existingPartner = await DeliveryPartner.findOne({ phoneNumber });
        if (existingPartner) {
            return res.status(400).json({ message: 'Delivery partner already exists' });
        }

        // Generate a custom ID if not provided
        let customId = id;
        if (!customId) {
            // Count existing partners to generate a sequential ID
            const count = await DeliveryPartner.countDocuments();
            customId = `DP${(count + 1).toString().padStart(3, '0')}`;
        }

        console.log('Creating new delivery partner with ID:', customId);

        const partner = new DeliveryPartner({
            id: customId,
            name,
            phoneNumber,
            email,
            vehicleType,
            vehicleNumber,
            addedBy: req.user?._id
        });

        await partner.save();
        console.log('Delivery partner created:', partner);
        res.status(201).json({ deliveryPartner: partner });
    } catch (error) {
        console.error('Error adding delivery partner:', error);
        console.error('Error details:', error.message);
        res.status(500).json({ message: 'Server error: ' + error.message });
    }
};

/**
 * Get all orders (admin view)
 * @route GET /api/admin/orders
 * @access Private (Admin only)
 */
const getAdminOrders = async (req, res) => {
    try {
        const orders = await Order.find()
            .populate('userId', 'name number')
            .populate('deliveryPartner', 'name phoneNumber')
            .sort({ createdAt: -1 });
        res.status(200).json({ orders });
    } catch (error) {
        console.error('Error fetching orders:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Get order by ID
 * @route GET /api/admin/orders/:id
 * @access Private (Admin only)
 */
const getOrderById = async (req, res) => {
    try {
        const order = await Order.findById(req.params.id)
            .populate('userId', 'name number')
            .populate('deliveryPartner', 'name phoneNumber');

        if (!order) {
            return res.status(404).json({ message: 'Order not found' });
        }

        res.status(200).json({ order });
    } catch (error) {
        console.error(`Error fetching order ${req.params.id}:`, error);
        res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Get orders by status
 * @route GET /api/admin/orders/status/:status
 * @access Private (Admin only)
 */
const getOrdersByStatus = async (req, res) => {
    try {
        const orders = await Order.find({ status: req.params.status })
            .populate('userId', 'name number')
            .populate('deliveryPartner', 'name phoneNumber')
            .sort({ createdAt: -1 });

        res.status(200).json({ orders });
    } catch (error) {
        console.error(`Error fetching orders with status ${req.params.status}:`, error);
        res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Get orders by user ID
 * @route GET /api/admin/orders/user/:userId
 * @access Private (Admin only)
 */
const getOrdersByUserId = async (req, res) => {
    try {
        const orders = await Order.find({ userId: req.params.userId })
            .populate('userId', 'name number')
            .populate('deliveryPartner', 'name phoneNumber')
            .sort({ createdAt: -1 });

        res.status(200).json({ orders });
    } catch (error) {
        console.error(`Error fetching orders for user ${req.params.userId}:`, error);
        res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Get orders by delivery partner ID with optional status filter
 * @route GET /api/admin/orders/delivery-partner/:partnerId
 * @access Private (Admin only)
 */
const getOrdersByDeliveryPartnerId = async (req, res) => {
    try {
        const partnerId = req.params.partnerId;
        console.log(`Fetching orders for delivery partner ${partnerId}`);

        // Simple approach: Get all orders and filter them
        const allOrders = await Order.find()
            .populate('userId', 'name number')
            .populate('deliveryPartner', 'name phoneNumber')
            .sort({ createdAt: -1 });

        console.log(`Found ${allOrders.length} total orders in the database`);

        // Filter orders for this delivery partner
        let filteredOrders = allOrders.filter(order => {
            // Check if deliveryPartner is this partner
            if (order.deliveryPartner) {
                // If deliveryPartner is an object with _id
                if (typeof order.deliveryPartner === 'object' && order.deliveryPartner._id) {
                    if (order.deliveryPartner._id.toString() === partnerId) {
                        return true;
                    }
                }

                // If deliveryPartner is a string ID
                if (typeof order.deliveryPartner === 'string' && order.deliveryPartner === partnerId) {
                    return true;
                }
            }

            return false;
        });

        // If we're using a custom ID like DP002, try to find the MongoDB ID
        if (partnerId.startsWith('DP') && filteredOrders.length === 0) {
            console.log('Custom ID format detected, looking up partner by ID');
            const partner = await DeliveryPartner.findOne({ id: partnerId });

            if (partner) {
                const mongoDbId = partner._id.toString();
                console.log(`Found MongoDB ID ${mongoDbId} for custom ID ${partnerId}`);

                // Filter again with the MongoDB ID
                filteredOrders = allOrders.filter(order => {
                    if (order.deliveryPartner) {
                        if (typeof order.deliveryPartner === 'object' && order.deliveryPartner._id) {
                            return order.deliveryPartner._id.toString() === mongoDbId;
                        }
                        return order.deliveryPartner.toString() === mongoDbId;
                    }
                    return false;
                });
            }
        }

        console.log(`Filtered to ${filteredOrders.length} orders for delivery partner ${partnerId}`);

        // Apply status filter if provided
        if (req.query.status) {
            console.log(`Filtering by status: ${req.query.status}`);

            // Map frontend status names to backend status names if needed
            const statusMap = {
                'pending': 'PLACED',
                'in-transit': 'OUT_FOR_DELIVERY',
                'delivered': 'DELIVERED',
                'cancelled': 'CANCELLED'
            };

            // Get the actual status to filter by (case insensitive)
            const requestStatus = req.query.status.toLowerCase();
            const filterStatus = statusMap[requestStatus] || req.query.status.toUpperCase();

            console.log(`Mapped status: ${requestStatus} -> ${filterStatus}`);

            filteredOrders = filteredOrders.filter(order => order.status === filterStatus);
            console.log(`After status filtering: ${filteredOrders.length} orders match status ${filterStatus}`);
        }

        res.status(200).json({ orders: filteredOrders });
    } catch (error) {
        console.error(`Error fetching orders for delivery partner ${req.params.partnerId}:`, error);
        res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Update order status
 * @route PATCH /api/admin/orders/:id/status
 * @access Private (Admin only)
 */
const updateOrderStatus = async (req, res) => {
    try {
        const { status, deliveryPartnerId } = req.body;

        if (!status) {
            return res.status(400).json({ message: 'Status is required' });
        }

        const updateData = { status };

        if (deliveryPartnerId) {
            updateData.deliveryPartner = deliveryPartnerId;
        }

        if (status === 'DELIVERED') {
            updateData.deliveredAt = new Date();
        }

        const order = await Order.findByIdAndUpdate(
            req.params.id,
            updateData,
            { new: true }
        )
            .populate('userId', 'name number')
            .populate('deliveryPartner', 'name phoneNumber');

        if (!order) {
            return res.status(404).json({ message: 'Order not found' });
        }

        // Send invoice email if order is delivered
        if (status === 'DELIVERED') {
            try {
                // Get the full order with populated user data
                const fullOrder = await Order.findById(req.params.id).populate('userId', 'name number email');

                if (fullOrder && fullOrder.userId && fullOrder.userId.email) {
                    console.log(`Sending invoice email for delivered order ${fullOrder.orderId} to ${fullOrder.userId.email}`);

                    // Process invoice asynchronously - don't wait for it to complete
                    processOrderInvoice(fullOrder._id)
                        .then(result => {
                            if (result.success) {
                                console.log(`Invoice email sent successfully for order ${fullOrder.orderId}`);
                            } else {
                                console.error(`Failed to send invoice email for order ${fullOrder.orderId}: ${result.error}`);
                            }
                        })
                        .catch(error => {
                            console.error(`Error sending invoice email for order ${fullOrder.orderId}:`, error);
                        });
                } else {
                    console.log(`User for order ${req.params.id} does not have an email address. Skipping invoice email.`);
                }
            } catch (emailError) {
                // Log error but don't fail the order status update
                console.error('Error sending invoice email:', emailError);
            }
        }

        // Get the io instance from the request
        const io = req.app.get('io');

        // Emit real-time update
        if (io) {
            // Notify admin room
            io.to('admin_room').emit('order_status_update', {
                orderId: order._id,
                status: order.status,
                timestamp: new Date().toISOString()
            });

            // Notify user
            io.to(`USER_${order.userId._id || order.userId}`).emit('order_status_update', {
                orderId: order._id,
                status: order.status,
                timestamp: new Date().toISOString()
            });

            // Notify delivery partner if assigned
            if (order.deliveryPartner) {
                const deliveryPartnerId = order.deliveryPartner._id || order.deliveryPartner;
                io.to(`DELIVERY_PARTNER_${deliveryPartnerId}`).emit('order_status_update', {
                    orderId: order._id,
                    status: order.status,
                    timestamp: new Date().toISOString()
                });
            }
        }

        // Send push notifications for status changes
        try {
            const { sendOrderStatusNotification } = require('../utils/pushNotificationUtils');
            const User = require('../models/User');

            // Send notifications for OUT_FOR_DELIVERY and DELIVERED status to users
            if (status === 'OUT_FOR_DELIVERY' || status === 'DELIVERED') {
                // Notify only the customer
                const customer = await User.findById(order.userId._id || order.userId).select('expoPushToken pushToken name');
                if (customer) {
                    const pushToken = customer.expoPushToken || customer.pushToken;
                    if (pushToken) {
                        console.log(`Sending ${status} notification to user:`, customer.name);
                        await sendOrderStatusNotification(
                            pushToken,
                            order.orderNumber,
                            status,
                            'USER'
                        );
                    } else {
                        console.log('No push token found for user:', customer.name);
                    }
                } else {
                    console.log('Customer not found for order:', order.orderNumber);
                }
            }
        } catch (notificationError) {
            console.error('Error sending admin status push notifications:', notificationError);
            // Don't fail the status update if notifications fail
        }

        res.status(200).json({ order });
    } catch (error) {
        console.error(`Error updating order ${req.params.id} status:`, error);
        res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Get all products (admin view)
 * @route GET /api/admin/products
 * @access Private (Admin only)
 */
const getAdminProducts = async (req, res) => {
    try {
        const products = await Product.find()
            .populate('category')
            .sort({ createdAt: -1 });
        console.log('Products found:', products.length);
        res.status(200).json({ products: products });
    } catch (error) {
        console.error('Error fetching products:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Get product by ID (admin view)
 * @route GET /api/admin/products/:id
 * @access Private (Admin only)
 */
const getProductById = async (req, res) => {
    try {
        const productId = req.params.id;
        console.log('Fetching product with ID:', productId);

        // Log all products to debug
        const allProducts = await Product.find({}, 'id _id name');
        console.log('Available products:', allProducts.map(p => ({
            _id: p._id.toString(),
            id: p.id,
            name: p.name
        })));

        let product = null;

        // Try multiple approaches to find the product

        // 1. First try by MongoDB ObjectId
        try {
            if (mongoose.Types.ObjectId.isValid(productId)) {
                console.log('Looking for product with MongoDB ObjectId');
                product = await Product.findById(productId).populate('category');
                if (product) {
                    console.log('Found product by MongoDB ObjectId:', product.name);
                }
            }
        } catch (idError) {
            console.error('Error finding by MongoDB ObjectId:', idError.message);
        }

        // 2. If not found, try by custom ID field
        if (!product) {
            console.log('Looking for product with custom ID field');
            product = await Product.findOne({ id: productId }).populate('category');
            if (product) {
                console.log('Found product by custom ID field:', product.name);
            }
        }

        // 3. If still not found, try by name (as a fallback)
        if (!product) {
            console.log('Looking for product by name');
            product = await Product.findOne({
                name: { $regex: new RegExp(productId, 'i') }
            }).populate('category');
            if (product) {
                console.log('Found product by name:', product.name);
            }
        }

        // If product is still not found, return 404
        if (!product) {
            console.log('Product not found with ID:', productId);
            return res.status(404).json({
                message: 'Product not found with ID: ' + productId,
                availableIds: allProducts.map(p => ({
                    _id: p._id.toString(),
                    id: p.id,
                    name: p.name
                }))
            });
        }

        console.log('Successfully found product:', product.name);
        res.status(200).json({ product: product });
    } catch (error) {
        console.error('Error fetching product:', error);
        res.status(500).json({
            message: 'Server error',
            error: error.message
        });
    }
};

/**
 * Create new product
 * @route POST /api/admin/products
 * @access Private (Admin only)
 */
const createProduct = async (req, res) => {
    try {
        const product = new Product(req.body);
        await product.save();
        res.status(201).json(product);
    } catch (error) {
        console.error('Error creating product:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Update product
 * @route PUT /api/admin/products/:id
 * @access Private (Admin only)
 */
const updateProduct = async (req, res) => {
    try {
        const product = await Product.findByIdAndUpdate(
            req.params.id,
            req.body,
            { new: true, runValidators: true }
        );
        if (!product) {
            return res.status(404).json({ message: 'Product not found' });
        }
        res.status(200).json(product);
    } catch (error) {
        console.error('Error updating product:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Delete product
 * @route DELETE /api/admin/products/:id
 * @access Private (Admin only)
 */
const deleteProduct = async (req, res) => {
    try {
        const product = await Product.findByIdAndDelete(req.params.id);
        if (!product) {
            return res.status(404).json({ message: 'Product not found' });
        }
        res.status(200).json({ message: 'Product deleted successfully' });
    } catch (error) {
        console.error('Error deleting product:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Get dashboard analytics
 * @route GET /api/admin/analytics/dashboard
 * @access Private (Admin only)
 */
const getDashboardAnalytics = async (req, res) => {
    try {
        // Get counts
        const userCount = await User.countDocuments();
        const activeUserCount = await User.countDocuments({
            lastActive: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
        });
        const newUserCount = await User.countDocuments({
            createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }
        });

        const deliveryPartnerCount = await DeliveryPartner.countDocuments();
        const activeDeliveryPartnerCount = await DeliveryPartner.countDocuments({
            lastActive: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
        });
        const availableDeliveryPartnerCount = await DeliveryPartner.countDocuments({
            isAvailable: true
        });

        const productCount = await Product.countDocuments();
        const outOfStockCount = await Product.countDocuments({ stock: 0 });
        const lowStockCount = await Product.countDocuments({ stock: { $gt: 0, $lt: 10 } });

        const pendingOrderCount = await Order.countDocuments({ status: 'PLACED' });
        const inTransitOrderCount = await Order.countDocuments({ status: 'OUT_FOR_DELIVERY' });
        const deliveredOrderCount = await Order.countDocuments({ status: 'DELIVERED' });
        const cancelledOrderCount = await Order.countDocuments({ status: 'CANCELLED' });
        const totalOrderCount = await Order.countDocuments();

        // Get top selling products (mock data for now)
        const topSellingProducts = ['Chicken Breast', 'Eggs', 'Mutton'];

        // Calculate total sales (mock data for now)
        const dailySales = 15600;
        const weeklySales = 98500;
        const monthlySales = 425000;
        const yearlySales = 5200000;

        // Calculate growth rates (mock data for now)
        const userGrowth = 8.1;
        const orderGrowth = 9.4;
        const salesGrowth = {
            daily: 12.5,
            weekly: 8.3,
            monthly: 15.2,
            yearly: 22.7
        };
        const deliveryPartnerGrowth = 5.6;

        // Mock revenue data
        const dailyRevenue = [2500, 3200, 2800, 3500, 4100, 3800, 4500];
        const weeklyRevenue = [18500, 21000, 19500, 23000];
        const monthlyRevenue = [350000, 380000, 420000, 425000, 450000, 410000];

        // Format the analytics data
        const analytics = {
            sales: {
                daily: dailySales,
                weekly: weeklySales,
                monthly: monthlySales,
                yearly: yearlySales,
                growth: salesGrowth
            },
            orders: {
                pending: pendingOrderCount,
                inTransit: inTransitOrderCount,
                delivered: deliveredOrderCount,
                cancelled: cancelledOrderCount,
                total: totalOrderCount,
                growth: orderGrowth
            },
            users: {
                total: userCount,
                active: activeUserCount,
                new: newUserCount,
                growth: userGrowth
            },
            deliveryPartners: {
                total: deliveryPartnerCount,
                active: activeDeliveryPartnerCount,
                available: availableDeliveryPartnerCount,
                growth: deliveryPartnerGrowth
            },
            products: {
                total: productCount,
                outOfStock: outOfStockCount,
                lowStock: lowStockCount,
                topSelling: topSellingProducts
            },
            revenue: {
                daily: dailyRevenue,
                weekly: weeklyRevenue,
                monthly: monthlyRevenue
            }
        };

        res.status(200).json({ analytics });
    } catch (error) {
        console.error('Error fetching dashboard analytics:', error);
        res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Update delivery partner
 * @route PUT /api/admin/delivery-partners/:id
 * @access Private (Admin only)
 */
const updateDeliveryPartner = async (req, res) => {
    try {
        console.log('Updating delivery partner with ID:', req.params.id);
        console.log('Update data:', req.body);

        // Normalize the request body to handle field name mismatches
        const normalizedData = { ...req.body };

        // Handle phone/phoneNumber field mismatch
        if (normalizedData.phone && !normalizedData.phoneNumber) {
            console.log('Converting phone field to phoneNumber:', normalizedData.phone);
            normalizedData.phoneNumber = normalizedData.phone;
            delete normalizedData.phone; // Remove the original phone field
        }

        console.log('Normalized data:', normalizedData);

        let partner;

        // Check if the ID is a MongoDB ObjectId or a string ID like "DP001"
        if (req.params.id.startsWith('DP')) {
            // Find by custom ID field (not MongoDB _id)
            console.log('Looking for delivery partner with custom ID:', req.params.id);

            // First try to find by the 'id' field
            partner = await DeliveryPartner.findOne({ id: req.params.id });

            // If not found, try to find by phoneNumber (as a fallback)
            if (!partner && normalizedData.phoneNumber) {
                console.log('Partner not found by id, trying phoneNumber:', normalizedData.phoneNumber);
                partner = await DeliveryPartner.findOne({ phoneNumber: normalizedData.phoneNumber });
            }

            if (!partner) {
                // If still not found, create a new partner
                console.log('Partner not found, creating new partner with ID:', req.params.id);
                partner = new DeliveryPartner({
                    ...normalizedData,
                    id: req.params.id
                });
            } else {
                // Update existing partner
                console.log('Found partner, updating fields');
                Object.keys(normalizedData).forEach(key => {
                    partner[key] = normalizedData[key];
                });
            }

            // Save the partner (either new or updated)
            await partner.save();
            console.log('Partner saved successfully');
        } else {
            try {
                // Use findOneAndUpdate for MongoDB ObjectId
                console.log('Using MongoDB ObjectId to find partner');
                partner = await DeliveryPartner.findOneAndUpdate(
                    { _id: req.params.id },
                    normalizedData,
                    { new: true, runValidators: true }
                );

                if (!partner) {
                    return res.status(404).json({ message: 'Delivery partner not found with ID: ' + req.params.id });
                }
            } catch (idError) {
                console.error('Error finding by _id:', idError.message);

                // If the ID is not a valid ObjectId, try to find by other fields
                if (normalizedData.phoneNumber) {
                    console.log('Trying to find by phoneNumber:', normalizedData.phoneNumber);
                    partner = await DeliveryPartner.findOneAndUpdate(
                        { phoneNumber: normalizedData.phoneNumber },
                        normalizedData,
                        { new: true, runValidators: true, upsert: true }
                    );
                } else {
                    throw idError; // Re-throw if we can't find by phone number
                }
            }
        }

        console.log('Updated delivery partner:', partner);
        res.status(200).json({ deliveryPartner: partner });
    } catch (error) {
        console.error('Error updating delivery partner:', error);
        console.error('Error details:', error.message);
        res.status(500).json({ message: 'Server error: ' + error.message });
    }
};

/**
 * Toggle delivery partner availability
 * @route PATCH /api/admin/delivery-partners/:id/toggle-availability
 * @access Private (Admin only)
 */
const toggleDeliveryPartnerAvailability = async (req, res) => {
    try {
        console.log('Toggling availability for delivery partner with ID:', req.params.id);

        let partner;

        // Check if the ID is a MongoDB ObjectId or a string ID like "DP001"
        if (req.params.id.startsWith('DP')) {
            // Find by custom ID field
            console.log('Looking for delivery partner with custom ID:', req.params.id);
            partner = await DeliveryPartner.findOne({ id: req.params.id });
        } else {
            try {
                // Try to find by MongoDB ObjectId
                console.log('Using MongoDB ObjectId to find partner');
                partner = await DeliveryPartner.findOne({ _id: req.params.id });
            } catch (idError) {
                console.error('Error finding by _id:', idError.message);
                // If the ID is not a valid ObjectId, return 404
                return res.status(404).json({ message: 'Delivery partner not found with ID: ' + req.params.id });
            }
        }

        if (!partner) {
            return res.status(404).json({ message: 'Delivery partner not found with ID: ' + req.params.id });
        }

        // Toggle availability
        partner.isAvailable = !partner.isAvailable;
        await partner.save();

        console.log('Toggled availability for delivery partner:', partner.name);
        console.log('New availability status:', partner.isAvailable);

        res.status(200).json({ deliveryPartner: partner });
    } catch (error) {
        console.error('Error toggling delivery partner availability:', error);
        console.error('Error details:', error.message);
        res.status(500).json({ message: 'Server error: ' + error.message });
    }
};

/**
 * Delete delivery partner
 * @route DELETE /api/admin/delivery-partners/:id
 * @access Private (Admin only)
 */
const deleteDeliveryPartner = async (req, res) => {
    try {
        console.log('Deleting delivery partner with ID:', req.params.id);

        let partner;

        // Check if the ID is a MongoDB ObjectId or a string ID like "DP001"
        if (req.params.id.startsWith('DP')) {
            // Find by custom ID field
            console.log('Looking for delivery partner with custom ID:', req.params.id);
            partner = await DeliveryPartner.findOne({ id: req.params.id });

            if (partner) {
                console.log('Found partner, deleting');
                await DeliveryPartner.deleteOne({ id: req.params.id });
            }
        } else {
            try {
                // First find the partner to return its data
                console.log('Using MongoDB ObjectId to find partner');
                partner = await DeliveryPartner.findOne({ _id: req.params.id });

                if (partner) {
                    console.log('Found partner, deleting');
                    await DeliveryPartner.deleteOne({ _id: req.params.id });
                }
            } catch (idError) {
                console.error('Error finding by _id:', idError.message);
                // If the ID is not a valid ObjectId, return 404
                return res.status(404).json({ message: 'Delivery partner not found with ID: ' + req.params.id });
            }
        }

        if (!partner) {
            return res.status(404).json({ message: 'Delivery partner not found with ID: ' + req.params.id });
        }

        console.log('Deleted delivery partner:', partner.name);
        res.status(200).json({ message: 'Delivery partner deleted successfully' });
    } catch (error) {
        console.error('Error deleting delivery partner:', error);
        console.error('Error details:', error.message);
        res.status(500).json({ message: 'Server error: ' + error.message });
    }
};

module.exports = {
    getAllUsers,
    getAllDeliveryPartners,
    addDeliveryPartner,
    updateDeliveryPartner,
    toggleDeliveryPartnerAvailability,
    deleteDeliveryPartner,
    getAdminOrders,
    getOrderById,
    getOrdersByStatus,
    getOrdersByUserId,
    getOrdersByDeliveryPartnerId,
    updateOrderStatus,
    getAdminProducts,
    getProductById,
    createProduct,
    updateProduct,
    deleteProduct,
    getDashboardAnalytics
};