<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Meat Now - Order #<%= typeof orderNumber !== 'undefined' ? orderNumber : orderId %> Delivered</title>
    <style>
        /* Base styles */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f9fafb;
        }

        /* Container */
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        /* Header */
        .header {
            background-color: #A31621;
            color: white;
            text-align: center;
            padding: 20px;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        .header p {
            margin: 5px 0 0;
            opacity: 0.9;
        }

        /* Logo */
        .logo {
            text-align: center;
            background-color: white;
            padding: 10px 0;
        }
        .logo img {
            max-width: 100px;
            height: auto;
        }

        /* Content */
        .content {
            padding: 25px;
        }

        /* Order status */
        .order-status {
            text-align: center;
            margin-bottom: 25px;
            padding: 15px;
            background-color: #f0fdf4;
            border-radius: 8px;
            border-left: 4px solid #22c55e;
        }
        .order-status h2 {
            margin: 0;
            color: #16a34a;
            font-size: 18px;
        }

        /* Order details */
        .order-details {
            background-color: #f9fafb;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 25px;
            border: 1px solid #e5e7eb;
        }
        .order-details h3 {
            margin-top: 0;
            color: #A31621;
            font-size: 18px;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .order-details table {
            width: 100%;
            border-collapse: collapse;
        }
        .order-details td {
            padding: 10px 8px;
            border-bottom: 1px solid #e5e7eb;
        }
        .order-details tr:last-child td {
            border-bottom: none;
        }
        .order-details .label {
            font-weight: 600;
            width: 150px;
            color: #4b5563;
        }

        /* Items table */
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 25px;
        }
        .items-table th {
            background-color: #f3f4f6;
            text-align: left;
            padding: 12px;
            font-weight: 600;
            color: #4b5563;
            border-bottom: 2px solid #e5e7eb;
        }
        .items-table td {
            padding: 12px;
            border-bottom: 1px solid #e5e7eb;
        }
        .items-table .amount {
            text-align: right;
        }
        .items-table .quantity {
            text-align: center;
        }
        .items-table .discount {
            color: #16a34a;
        }

        /* Price summary */
        .price-summary {
            background-color: #f9fafb;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 25px;
            border: 1px solid #e5e7eb;
        }
        .price-summary h3 {
            margin-top: 0;
            color: #A31621;
            font-size: 18px;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .price-summary table {
            width: 100%;
            border-collapse: collapse;
        }
        .price-summary td {
            padding: 8px;
        }
        .price-summary .label {
            font-weight: normal;
            color: #4b5563;
        }
        .price-summary .amount {
            text-align: right;
            font-weight: 600;
        }
        .price-summary .discount {
            color: #16a34a;
        }
        .price-summary .total-row {
            border-top: 2px solid #e5e7eb;
            font-weight: 700;
        }
        .price-summary .total-row .label {
            font-weight: 700;
            color: #111827;
        }
        .price-summary .total-row .amount {
            color: #A31621;
            font-size: 18px;
        }

        /* Contact */
        .contact {
            border-top: 1px solid #e5e7eb;
            padding-top: 15px;
            margin-top: 25px;
            margin-bottom: 25px;
        }
        .contact h4 {
            color: #4b5563;
            margin-top: 0;
            margin-bottom: 10px;
            font-size: 15px;
            font-weight: 600;
        }
        .contact a {
            color: #A31621;
            text-decoration: none;
            font-weight: 600;
        }
        .contact a:hover {
            text-decoration: underline;
        }

        /* Thank you */
        .thank-you {
            text-align: center;
            margin: 30px 0;
        }
        .thank-you p {
            font-size: 18px;
            color: #A31621;
            font-weight: 600;
        }

        /* Footer */
        .footer {
            text-align: center;
            background-color: #f3f4f6;
            padding: 20px;
            color: #6b7280;
            font-size: 14px;
        }

        /* Coins info */
        .coins-info {
            background-color: #fffbeb;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 25px;
            border-left: 4px solid #f59e0b;
        }
        .coins-info h4 {
            color: #d97706;
            margin-top: 0;
            margin-bottom: 10px;
            font-size: 16px;
        }
        .coins-info p {
            margin: 5px 0;
        }

        /* Responsive */
        @media only screen and (max-width: 600px) {
            .container {
                width: 100%;
                border-radius: 0;
            }
            .content {
                padding: 15px;
            }
            .order-details .label {
                width: 120px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <img src="https://res.cloudinary.com/dmjwppr6t/image/upload/v1747812882/logo.png" alt="MeatNow Logo">
        </div>

        <div class="header">
            <h1>Order Delivered</h1>
        </div>

        <div class="content">
            <p>Dear <%= customerName %>,</p>

            <p style="font-size: 16px; margin-bottom: 20px;">Your order has been delivered. Please find your invoice attached to this email.</p>

            <div class="order-details">
                <h3>Order Information</h3>
                <table>
                    <tr>
                        <td class="label">Order Number:</td>
                        <td><strong>#<%= typeof orderNumber !== 'undefined' ? orderNumber : orderId %></strong></td>
                    </tr>
                    <tr>
                        <td class="label">Order Date:</td>
                        <td><%= orderDate %></td>
                    </tr>
                    <tr>
                        <td class="label">Payment Method:</td>
                        <td><%= paymentMethod.toUpperCase() %></td>
                    </tr>
                    <tr>
                        <td class="label">Delivery Address:</td>
                        <td><%= deliveryAddress %></td>
                    </tr>
                </table>
            </div>

            <h3>Order Items</h3>
            <table class="items-table">
                <thead>
                    <tr>
                        <th>Item</th>
                        <th class="quantity">Qty</th>
                        <th>Price</th>
                        <th class="amount">Total</th>
                    </tr>
                </thead>
                <tbody>
                    <% items.forEach(item => { %>
                    <tr>
                        <td><%= item.name %></td>
                        <td class="quantity"><%= item.quantity %></td>
                        <td>
                            <%
                            // Ensure price is defined and valid number
                            const price = typeof item.price === 'number' ? item.price : 0;
                            %>
                            ₹<%= price.toFixed(2) %>
                        </td>
                        <td class="amount">₹<%= (item.quantity * price).toFixed(2) %></td>
                    </tr>
                    <% }); %>
                </tbody>
            </table>

            <div class="price-summary">
                <h3>Price Details</h3>
                <table>
                    <%
                    // Calculate the total from the items
                    let itemsTotal = 0;
                    items.forEach(item => {
                        const price = typeof item.price === 'number' ? item.price : 0;
                        itemsTotal += (item.quantity * price);
                    });
                    %>
                    <tr>
                        <td class="label">Total Amount:</td>
                        <td class="amount">₹<%= itemsTotal.toFixed(2) %></td>
                    </tr>
                    <%
                    // Calculate product discount (difference between items total and original amount)
                    const productDiscount = Math.max(0, itemsTotal - originalAmount);
                    if (productDiscount > 0) {
                    %>
                    <tr style="border-bottom: 1px solid #e5e7eb;">
                        <td class="label">Discount on Products:</td>
                        <td class="amount" style="color: #dc2626;">-₹<%= productDiscount.toFixed(2) %></td>
                    </tr>
                    <% } %>
                    <tr>
                        <td class="label">Discounted Amount:</td>
                        <td class="amount">₹<%= originalAmount.toFixed(2) %></td>
                    </tr>
                    <% if (deliveryFee > 0) { %>
                    <tr>
                        <td class="label">Delivery Fee:</td>
                        <td class="amount">₹<%= deliveryFee.toFixed(2) %></td>
                    </tr>
                    <% } else if (originalAmount >= 499) { %>
                    <tr>
                        <td class="label">Delivery Fee:</td>
                        <td class="amount discount">Free (Orders above ₹499)</td>
                    </tr>
                    <% } %>
                    <% if (couponDiscount > 0) { %>
                    <tr>
                        <td class="label">Coupon Discount (<%= appliedCoupon %>):</td>
                        <td class="amount discount">-₹<%= couponDiscount.toFixed(2) %></td>
                    </tr>
                    <% } %>
                    <% if (coinsDiscount > 0) { %>
                    <tr>
                        <td class="label">Coins Discount:</td>
                        <td class="amount discount">-₹<%= coinsDiscount.toFixed(2) %></td>
                    </tr>
                    <% } %>
                    <%
                    // Calculate grand total: Original Amount + Delivery Fee - Coupon Discount - Coins Discount
                    const deliveryFeeAmount = deliveryFee > 0 ? deliveryFee : 0;
                    const couponDiscountAmount = typeof couponDiscount === 'number' ? couponDiscount : 0;
                    const coinsDiscountAmount = typeof coinsDiscount === 'number' ? coinsDiscount : 0;
                    const grandTotal = originalAmount + deliveryFeeAmount - couponDiscountAmount - coinsDiscountAmount;
                    %>
                    <tr class="total-row">
                        <td class="label">Grand Total:</td>
                        <td class="amount">₹<%= grandTotal.toFixed(2) %></td>
                    </tr>
                </table>
            </div>


            <div class="contact" style="text-align: center; margin-top: 30px; margin-bottom: 30px;">
                <h4 style="font-size: 16px; color: #A31621; margin-bottom: 10px;">Questions about your order?</h4>
                <p>
                    <a href="mailto:<EMAIL>" style="color: #A31621; margin-right: 15px;"><img src="https://res.cloudinary.com/dmjwppr6t/image/upload/v1747815624/email_mxnzvi.jpg" alt="Email" style="width: 16px; vertical-align: middle; margin-right: 5px;"><EMAIL></a>
                    <a href="tel:+918825549901" style="color: #A31621;"><img src="https://res.cloudinary.com/dmjwppr6t/image/upload/v1747815617/phone_tbzhud.png" alt="Phone" style="width: 16px; vertical-align: middle; margin-right: 5px;">+91 8825549901</a>
                </p>
            </div>

            <div class="thank-you">
                <p>Thank you for choosing MeatNow!</p>
            </div>
        </div>

        <div class="footer">
            <p>
                MeatNow - Fresh Meat Delivered to Your Doorstep<br>
                © <%= new Date().getFullYear() %> MeatNow. All rights reserved.
            </p>
        </div>
    </div>
</body>
</html>
